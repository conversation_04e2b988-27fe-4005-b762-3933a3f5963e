#!/usr/bin/env python3
"""
主持人功能数据库迁移脚本
添加主持人相关字段到现有数据库
"""

from backend.app import app
from backend.models import db
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_database():
    """迁移数据库，添加主持人相关字段"""
    with app.app_context():
        try:
            # 使用SQLAlchemy的text()来执行原生SQL
            from sqlalchemy import text

            # 检查并添加Agent表的新字段
            try:
                # 检查is_moderator字段是否存在
                result = db.session.execute(text("PRAGMA table_info(agents)"))
                columns = [row[1] for row in result]

                if 'is_moderator' not in columns:
                    db.session.execute(text("ALTER TABLE agents ADD COLUMN is_moderator BOOLEAN DEFAULT 0"))
                    logger.info("添加agents.is_moderator字段")

                if 'moderator_config' not in columns:
                    db.session.execute(text("ALTER TABLE agents ADD COLUMN moderator_config TEXT"))
                    logger.info("添加agents.moderator_config字段")

            except Exception as e:
                logger.error(f"迁移agents表失败: {e}")

            # 检查并添加Discussion表的新字段
            try:
                result = db.session.execute(text("PRAGMA table_info(discussions)"))
                columns = [row[1] for row in result]

                if 'moderator_id' not in columns:
                    db.session.execute(text("ALTER TABLE discussions ADD COLUMN moderator_id VARCHAR(36)"))
                    logger.info("添加discussions.moderator_id字段")

                if 'moderator_summaries' not in columns:
                    db.session.execute(text("ALTER TABLE discussions ADD COLUMN moderator_summaries TEXT"))
                    logger.info("添加discussions.moderator_summaries字段")

                if 'topic_relevance_score' not in columns:
                    db.session.execute(text("ALTER TABLE discussions ADD COLUMN topic_relevance_score FLOAT DEFAULT 1.0"))
                    logger.info("添加discussions.topic_relevance_score字段")

                if 'moderator_interventions' not in columns:
                    db.session.execute(text("ALTER TABLE discussions ADD COLUMN moderator_interventions INTEGER DEFAULT 0"))
                    logger.info("添加discussions.moderator_interventions字段")

            except Exception as e:
                logger.error(f"迁移discussions表失败: {e}")

            # 创建ModeratorAction表
            try:
                result = db.session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='moderator_actions'"))
                if not result.fetchone():
                    db.session.execute(text("""
                        CREATE TABLE moderator_actions (
                            id VARCHAR(36) PRIMARY KEY,
                            discussion_id VARCHAR(36) NOT NULL,
                            moderator_id VARCHAR(36) NOT NULL,
                            action_type VARCHAR(50) NOT NULL,
                            action_content TEXT NOT NULL,
                            context_data TEXT,
                            relevance_score FLOAT,
                            consensus_score_before FLOAT,
                            consensus_score_after FLOAT,
                            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (discussion_id) REFERENCES discussions (id),
                            FOREIGN KEY (moderator_id) REFERENCES agents (id)
                        )
                    """))
                    logger.info("创建moderator_actions表")

            except Exception as e:
                logger.error(f"创建moderator_actions表失败: {e}")

            # 提交更改
            db.session.commit()

            logger.info("数据库迁移完成")

        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    migrate_database()
