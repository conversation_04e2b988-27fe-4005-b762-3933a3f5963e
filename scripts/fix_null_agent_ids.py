#!/usr/bin/env python3
"""
修复数据库中agent_id为NULL的消息记录
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.app import create_app
from backend.models import db, Message, Agent
from sqlalchemy import text

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_null_agent_ids():
    """修复agent_id为NULL的消息记录"""
    app = create_app()
    
    with app.app_context():
        try:
            # 查找所有agent_id为NULL的消息
            null_messages = Message.query.filter(Message.agent_id.is_(None)).all()
            
            if not null_messages:
                logger.info("没有发现agent_id为NULL的消息记录")
                return
            
            logger.info(f"发现 {len(null_messages)} 条agent_id为NULL的消息记录")
            
            # 获取第一个可用的agent作为默认值
            default_agent = Agent.query.first()
            
            if not default_agent:
                logger.error("数据库中没有可用的智能体，无法修复NULL的agent_id")
                return
            
            logger.info(f"使用默认智能体 '{default_agent.name}' (ID: {default_agent.id}) 作为默认值")
            
            # 选择处理方式
            print("\n请选择处理方式：")
            print("1. 删除这些无效的消息记录")
            print("2. 将这些消息分配给默认智能体")
            print("3. 取消操作")
            
            choice = input("请输入选择 (1/2/3): ").strip()
            
            if choice == '1':
                # 删除无效消息
                for message in null_messages:
                    db.session.delete(message)
                db.session.commit()
                logger.info(f"已删除 {len(null_messages)} 条无效消息记录")
                
            elif choice == '2':
                # 分配给默认智能体
                for message in null_messages:
                    message.agent_id = default_agent.id
                db.session.commit()
                logger.info(f"已将 {len(null_messages)} 条消息分配给默认智能体")
                
            elif choice == '3':
                logger.info("操作已取消")
                return
                
            else:
                logger.error("无效的选择")
                return
            
            # 验证修复结果
            remaining_null = Message.query.filter(Message.agent_id.is_(None)).count()
            if remaining_null == 0:
                logger.info("所有NULL的agent_id已成功修复")
            else:
                logger.warning(f"仍有 {remaining_null} 条消息的agent_id为NULL")
                
        except Exception as e:
            db.session.rollback()
            logger.error(f"修复过程中发生错误: {e}")
            raise

def check_database_integrity():
    """检查数据库完整性"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查NULL的agent_id
            null_agent_count = Message.query.filter(Message.agent_id.is_(None)).count()
            logger.info(f"agent_id为NULL的消息数量: {null_agent_count}")
            
            # 检查无效的agent_id引用
            invalid_refs = db.session.execute(text("""
                SELECT COUNT(*) 
                FROM messages m 
                LEFT JOIN agents a ON m.agent_id = a.id 
                WHERE m.agent_id IS NOT NULL AND a.id IS NULL
            """)).scalar()
            logger.info(f"引用不存在智能体的消息数量: {invalid_refs}")
            
            # 检查NULL的discussion_id
            null_discussion_count = Message.query.filter(Message.discussion_id.is_(None)).count()
            logger.info(f"discussion_id为NULL的消息数量: {null_discussion_count}")
            
            # 总消息数量
            total_messages = Message.query.count()
            logger.info(f"总消息数量: {total_messages}")
            
            if null_agent_count > 0 or invalid_refs > 0 or null_discussion_count > 0:
                logger.warning("数据库存在完整性问题，建议运行修复脚本")
                return False
            else:
                logger.info("数据库完整性检查通过")
                return True
                
        except Exception as e:
            logger.error(f"检查数据库完整性时发生错误: {e}")
            return False

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--check':
        check_database_integrity()
    else:
        fix_null_agent_ids()
