#!/usr/bin/env python3
"""
迁移数据库外键约束，添加级联删除支持
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.app import create_app
from backend.models import db
from sqlalchemy import text

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_cascade_constraints():
    """迁移数据库外键约束"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始迁移数据库外键约束...")
            
            # SQLite不支持直接修改外键约束，需要重建表
            # 但我们可以通过重新创建表来实现
            
            # 1. 备份现有数据
            logger.info("备份现有数据...")
            
            # 获取所有表的数据
            messages_data = db.session.execute(text("SELECT * FROM messages")).fetchall()
            discussions_data = db.session.execute(text("SELECT * FROM discussions")).fetchall()
            agents_data = db.session.execute(text("SELECT * FROM agents")).fetchall()
            llm_configs_data = db.session.execute(text("SELECT * FROM llm_configs")).fetchall()
            
            logger.info(f"备份完成: {len(messages_data)} 条消息, {len(discussions_data)} 个讨论, {len(agents_data)} 个智能体, {len(llm_configs_data)} 个LLM配置")
            
            # 2. 删除所有表
            logger.info("删除现有表...")
            db.session.execute(text("PRAGMA foreign_keys = OFF"))
            
            # 按依赖顺序删除表
            tables_to_drop = ['messages', 'discussions', 'agents', 'llm_configs', 'app_settings', 'user_preferences']
            for table in tables_to_drop:
                try:
                    db.session.execute(text(f"DROP TABLE IF EXISTS {table}"))
                    logger.info(f"删除表 {table}")
                except Exception as e:
                    logger.warning(f"删除表 {table} 时出错: {e}")
            
            db.session.commit()
            
            # 3. 重新创建表（使用新的约束）
            logger.info("重新创建表...")
            db.create_all()
            logger.info("表创建完成")
            
            # 4. 恢复数据
            logger.info("恢复数据...")
            
            # 恢复LLM配置
            for row in llm_configs_data:
                db.session.execute(text("""
                    INSERT INTO llm_configs (id, name, provider, model, api_key, base_url, temperature, max_tokens, system_prompt, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """), row)
            
            # 恢复智能体
            for row in agents_data:
                db.session.execute(text("""
                    INSERT INTO agents (id, name, avatar, expertise, thinking_style, personality, tools, is_active, llm_config_id, is_moderator, moderator_config, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """), row)
            
            # 恢复讨论
            for row in discussions_data:
                # 处理可能缺少的新字段
                row_list = list(row)
                while len(row_list) < 11:  # 确保有足够的字段
                    row_list.append(None)
                
                db.session.execute(text("""
                    INSERT INTO discussions (id, topic, mode, participants, status, consensus, consensus_score, created_at, moderator_id, moderator_summaries, topic_relevance_score, moderator_interventions, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """), row_list[:13])
            
            # 恢复消息
            for row in messages_data:
                db.session.execute(text("""
                    INSERT INTO messages (id, agent_id, discussion_id, content, message_type, reply_to, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """), row)
            
            db.session.commit()
            logger.info("数据恢复完成")
            
            # 5. 启用外键约束
            db.session.execute(text("PRAGMA foreign_keys = ON"))
            db.session.commit()
            
            logger.info("外键约束迁移完成")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"迁移过程中发生错误: {e}")
            raise

def test_cascade_delete():
    """测试级联删除功能"""
    app = create_app()
    
    with app.app_context():
        try:
            from backend.models import Agent, Discussion, Message, LLMConfig
            
            logger.info("测试级联删除功能...")
            
            # 创建测试数据
            logger.info("创建测试数据...")
            
            # 创建测试LLM配置
            test_llm = LLMConfig(
                id="test-cascade-llm",
                name="测试级联LLM",
                provider="test",
                model="test-model",
                api_key="test-key"
            )
            db.session.add(test_llm)
            
            # 创建测试智能体
            test_agent = Agent(
                id="test-cascade-agent",
                name="测试级联智能体",
                avatar="🧪",
                expertise='["测试"]',
                thinking_style="测试",
                personality="测试",
                tools='[]',
                llm_config_id="test-cascade-llm"
            )
            db.session.add(test_agent)
            
            # 创建测试讨论
            test_discussion = Discussion(
                id="test-cascade-discussion",
                topic="测试级联删除",
                mode="free",
                participants='["test-cascade-agent"]',
                status="active"
            )
            db.session.add(test_discussion)
            
            # 创建测试消息
            test_message = Message(
                id="test-cascade-message",
                agent_id="test-cascade-agent",
                discussion_id="test-cascade-discussion",
                content="测试级联删除消息",
                message_type="statement"
            )
            db.session.add(test_message)
            
            db.session.commit()
            logger.info("测试数据创建完成")
            
            # 测试删除讨论（应该级联删除消息）
            logger.info("测试删除讨论...")
            discussion_to_delete = Discussion.query.get("test-cascade-discussion")
            if discussion_to_delete:
                db.session.delete(discussion_to_delete)
                db.session.commit()
                
                # 检查消息是否被删除
                remaining_message = Message.query.get("test-cascade-message")
                if remaining_message is None:
                    logger.info("✓ 讨论删除时消息被正确级联删除")
                else:
                    logger.error("✗ 讨论删除时消息未被级联删除")
            
            # 测试删除智能体（应该级联删除相关消息）
            logger.info("测试删除智能体...")
            agent_to_delete = Agent.query.get("test-cascade-agent")
            if agent_to_delete:
                db.session.delete(agent_to_delete)
                db.session.commit()
                logger.info("✓ 智能体删除成功")
            
            # 清理测试数据
            logger.info("清理测试数据...")
            LLMConfig.query.filter_by(id="test-cascade-llm").delete()
            db.session.commit()
            
            logger.info("级联删除测试完成")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"测试过程中发生错误: {e}")
            raise

def check_constraints():
    """检查当前的外键约束"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("检查当前的外键约束...")
            
            # 检查外键约束是否启用
            fk_enabled = db.session.execute(text("PRAGMA foreign_keys")).scalar()
            logger.info(f"外键约束状态: {'启用' if fk_enabled else '禁用'}")
            
            # 检查各表的外键信息
            tables = ['messages', 'discussions', 'agents']
            for table in tables:
                try:
                    fk_info = db.session.execute(text(f"PRAGMA foreign_key_list({table})")).fetchall()
                    if fk_info:
                        logger.info(f"表 {table} 的外键约束:")
                        for fk in fk_info:
                            logger.info(f"  - {fk[3]} -> {fk[2]}.{fk[4]} (on_delete: {fk[6]})")
                    else:
                        logger.info(f"表 {table} 没有外键约束")
                except Exception as e:
                    logger.warning(f"检查表 {table} 的外键约束时出错: {e}")
            
        except Exception as e:
            logger.error(f"检查约束时发生错误: {e}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == '--check':
            check_constraints()
        elif sys.argv[1] == '--test':
            test_cascade_delete()
        elif sys.argv[1] == '--migrate':
            migrate_cascade_constraints()
        else:
            print("用法: python migrate_cascade_constraints.py [--check|--test|--migrate]")
    else:
        print("用法: python migrate_cascade_constraints.py [--check|--test|--migrate]")
        print("  --check   检查当前的外键约束")
        print("  --test    测试级联删除功能")
        print("  --migrate 执行约束迁移")
