#!/usr/bin/env python3
"""
测试消息验证功能
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.app import create_app
from backend.models import db, Message, Agent, Discussion, LLMConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_message_validation():
    """测试消息验证功能"""
    app = create_app()
    
    with app.app_context():
        try:
            # 测试1: 尝试创建没有agent_id的消息
            logger.info("测试1: 创建没有agent_id的消息")
            try:
                message = Message(
                    id="test-1",
                    agent_id=None,  # 这应该失败
                    discussion_id="test-discussion",
                    content="测试消息",
                    message_type="statement"
                )
                logger.error("测试1失败: 应该抛出ValueError")
            except ValueError as e:
                logger.info(f"测试1通过: {e}")
            
            # 测试2: 尝试创建没有discussion_id的消息
            logger.info("测试2: 创建没有discussion_id的消息")
            try:
                message = Message(
                    id="test-2",
                    agent_id="test-agent",
                    discussion_id=None,  # 这应该失败
                    content="测试消息",
                    message_type="statement"
                )
                logger.error("测试2失败: 应该抛出ValueError")
            except ValueError as e:
                logger.info(f"测试2通过: {e}")
            
            # 测试3: 尝试创建没有content的消息
            logger.info("测试3: 创建没有content的消息")
            try:
                message = Message(
                    id="test-3",
                    agent_id="test-agent",
                    discussion_id="test-discussion",
                    content="",  # 这应该失败
                    message_type="statement"
                )
                logger.error("测试3失败: 应该抛出ValueError")
            except ValueError as e:
                logger.info(f"测试3通过: {e}")
            
            # 测试4: 尝试创建没有message_type的消息
            logger.info("测试4: 创建没有message_type的消息")
            try:
                message = Message(
                    id="test-4",
                    agent_id="test-agent",
                    discussion_id="test-discussion",
                    content="测试消息",
                    message_type=""  # 这应该失败
                )
                logger.error("测试4失败: 应该抛出ValueError")
            except ValueError as e:
                logger.info(f"测试4通过: {e}")
            
            # 测试5: 创建有效的消息
            logger.info("测试5: 创建有效的消息")
            try:
                message = Message(
                    id="test-5",
                    agent_id="test-agent",
                    discussion_id="test-discussion",
                    content="测试消息",
                    message_type="statement"
                )
                logger.info("测试5通过: 成功创建有效消息")
            except ValueError as e:
                logger.error(f"测试5失败: {e}")
            
            logger.info("所有测试完成")
            
        except Exception as e:
            logger.error(f"测试过程中发生错误: {e}")
            raise

def create_test_data():
    """创建测试数据"""
    app = create_app()
    
    with app.app_context():
        try:
            # 创建测试LLM配置
            llm_config = LLMConfig(
                id="test-llm-config",
                name="测试配置",
                provider="openai",
                model="gpt-3.5-turbo",
                api_key="test-key"
            )
            db.session.merge(llm_config)
            
            # 创建测试智能体
            agent = Agent(
                id="test-agent",
                name="测试智能体",
                avatar="🤖",
                expertise='["测试"]',
                thinking_style="测试思维",
                personality="测试性格",
                tools='[]',
                llm_config_id="test-llm-config"
            )
            db.session.merge(agent)
            
            # 创建测试讨论
            discussion = Discussion(
                id="test-discussion",
                topic="测试话题",
                mode="free",
                participants='["test-agent"]',
                status="active"
            )
            db.session.merge(discussion)
            
            db.session.commit()
            logger.info("测试数据创建成功")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"创建测试数据时发生错误: {e}")
            raise

def cleanup_test_data():
    """清理测试数据"""
    app = create_app()
    
    with app.app_context():
        try:
            # 删除测试数据
            Message.query.filter(Message.id.like('test-%')).delete()
            Discussion.query.filter_by(id="test-discussion").delete()
            Agent.query.filter_by(id="test-agent").delete()
            LLMConfig.query.filter_by(id="test-llm-config").delete()
            
            db.session.commit()
            logger.info("测试数据清理完成")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"清理测试数据时发生错误: {e}")
            raise

if __name__ == '__main__':
    if len(sys.argv) > 1:
        if sys.argv[1] == '--setup':
            create_test_data()
        elif sys.argv[1] == '--cleanup':
            cleanup_test_data()
        elif sys.argv[1] == '--test':
            test_message_validation()
        else:
            print("用法: python test_message_validation.py [--setup|--test|--cleanup]")
    else:
        # 运行完整测试
        create_test_data()
        test_message_validation()
        cleanup_test_data()
