#!/usr/bin/env python3
"""
数据库初始化脚本
"""

from app import app
from models import db, AppSettings, UserPreferences
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            logger.info("数据库表创建成功")
            
            # 创建默认设置
            if not AppSettings.query.first():
                default_settings = AppSettings(
                    version='1.0.0',
                    auto_save=True,
                    max_stored_discussions=100,
                    default_discussion_mode='free',
                    theme='light'
                )
                db.session.add(default_settings)
                logger.info("创建默认应用设置")
            
            # 创建默认用户偏好
            if not UserPreferences.query.first():
                default_preferences = UserPreferences(
                    default_agent_count=3,
                    preferred_llm_provider='openai',
                    auto_start_discussion=False,
                    show_advanced_options=False,
                    notifications_enabled=True,
                    export_format='json'
                )
                db.session.add(default_preferences)
                logger.info("创建默认用户偏好")
            
            db.session.commit()
            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    init_database()
