# 多智能体讨论系统后端

这是多智能体讨论系统的Python Flask后端服务，提供数据持久化和RESTful API接口。

## 功能特性

- 🗄️ SQLite数据库存储
- 🔄 RESTful API接口
- 🤖 智能体管理
- 💬 讨论记录管理
- ⚙️ LLM配置管理
- 📊 数据导入/导出
- 🔧 应用设置管理

## 技术栈

- **Flask**: Web框架
- **SQLAlchemy**: ORM数据库操作
- **SQLite**: 轻量级数据库
- **Flask-CORS**: 跨域支持
- **Marshmallow**: 数据序列化

## 快速开始

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 环境配置

复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量。

### 3. 初始化数据库

```bash
python init_db.py
```

### 4. 启动服务

```bash
python run.py
```

或者直接运行：
```bash
python app.py
```

服务将在 `http://localhost:5000` 启动。

## API接口

### 健康检查
- `GET /` - 服务信息
- `GET /health` - 健康检查

### LLM配置管理
- `GET /api/llm-configs` - 获取所有LLM配置
- `POST /api/llm-configs` - 创建LLM配置
- `PUT /api/llm-configs/<id>` - 更新LLM配置
- `DELETE /api/llm-configs/<id>` - 删除LLM配置

### 智能体管理
- `GET /api/agents` - 获取所有智能体
- `POST /api/agents` - 创建智能体
- `PUT /api/agents/<id>` - 更新智能体
- `DELETE /api/agents/<id>` - 删除智能体

### 讨论管理
- `GET /api/discussions` - 获取所有讨论
- `POST /api/discussions` - 创建讨论
- `PUT /api/discussions/<id>` - 更新讨论
- `DELETE /api/discussions/<id>` - 删除讨论
- `POST /api/discussions/<id>/messages` - 添加消息

### 设置管理
- `GET /api/settings` - 获取应用设置
- `PUT /api/settings` - 更新应用设置
- `GET /api/preferences` - 获取用户偏好
- `PUT /api/preferences` - 更新用户偏好

### 数据管理
- `GET /api/data/export` - 导出所有数据
- `POST /api/data/import` - 导入数据
- `DELETE /api/data/clear` - 清除所有数据

## 数据模型

### LLMConfig (LLM配置)
```json
{
  "id": "uuid",
  "name": "配置名称",
  "provider": "openai|anthropic|azure|custom",
  "model": "模型名称",
  "apiKey": "API密钥",
  "baseURL": "基础URL（可选）",
  "temperature": 0.7,
  "maxTokens": 1000,
  "systemPrompt": "系统提示（可选）"
}
```

### Agent (智能体)
```json
{
  "id": "uuid",
  "name": "智能体名称",
  "avatar": "头像URL",
  "expertise": ["专业领域"],
  "thinkingStyle": "思维方式",
  "personality": "性格特征",
  "tools": ["可用工具"],
  "isActive": true,
  "llmConfig": "LLMConfig对象"
}
```

### Discussion (讨论)
```json
{
  "id": "uuid",
  "topic": "讨论主题",
  "mode": "moderator|free",
  "participants": ["智能体ID"],
  "status": "active|consensus|ended",
  "consensus": "共识内容",
  "consensusScore": 0.8,
  "createdAt": "ISO时间戳",
  "messages": ["Message对象数组"]
}
```

### Message (消息)
```json
{
  "id": "uuid",
  "agentId": "智能体ID",
  "content": "消息内容",
  "type": "statement|question|agreement|disagreement",
  "replyTo": "回复的消息ID（可选）",
  "timestamp": "ISO时间戳"
}
```

## 开发说明

### 项目结构
```
backend/
├── app.py              # Flask应用主文件
├── models.py           # 数据库模型
├── routes.py           # API路由
├── init_db.py          # 数据库初始化
├── run.py              # 启动脚本
├── requirements.txt    # Python依赖
├── .env.example        # 环境配置示例
└── README.md          # 说明文档
```

### 数据库
使用SQLite作为默认数据库，数据文件保存在 `multi_agent_system.db`。

### 日志
应用使用Python标准logging模块，日志级别可通过环境变量 `LOG_LEVEL` 配置。

### 错误处理
所有API接口都包含适当的错误处理和日志记录。

## 部署

### 生产环境
1. 设置环境变量 `FLASK_ENV=production`
2. 配置适当的数据库URL
3. 使用WSGI服务器（如Gunicorn）部署

### Docker部署
可以创建Dockerfile进行容器化部署。

## 注意事项

- API密钥等敏感信息会存储在数据库中，请确保数据库安全
- 建议定期备份数据库文件
- 生产环境请使用更安全的SECRET_KEY
