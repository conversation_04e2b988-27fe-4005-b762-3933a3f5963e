@echo off
chcp 65001 >nul

echo 🚀 启动多智能体讨论系统...

REM 检查是否安装了必要的依赖
echo 📦 检查依赖...

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装 Python
    pause
    exit /b 1
)

REM 检查npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装，请先安装 npm
    pause
    exit /b 1
)

REM 检查pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip 未安装，请先安装 pip
    pause
    exit /b 1
)

echo ✅ 依赖检查完成

REM 安装前端依赖
echo 📦 安装前端依赖...
cd frontend
if not exist "node_modules" (
    npm install
)
cd ..

REM 安装后端依赖
echo 📦 安装后端依赖...
cd backend
if not exist "venv" (
    echo 创建Python虚拟环境...
    python -m venv venv
)

echo 激活虚拟环境并安装依赖...
call venv\Scripts\activate.bat
pip install -r requirements.txt

REM 初始化数据库
echo 🗄️ 初始化数据库...
python init_db.py

cd ..

REM 创建环境变量文件（如果不存在）
if not exist ".env" (
    echo 📝 创建环境变量文件...
    copy .env.example .env
)

if not exist "backend\.env" (
    echo 📝 创建后端环境变量文件...
    copy backend\.env.example backend\.env
)

echo 🎉 准备工作完成！
echo.
echo 现在将启动前端和后端服务...
echo 前端地址: http://localhost:3000
echo 后端地址: http://localhost:5000
echo.
echo 按 Ctrl+C 停止所有服务
echo.

REM 启动后端服务（后台运行）
echo 🔧 启动后端服务...
cd backend
call venv\Scripts\activate.bat
start /b python app.py
cd ..

REM 等待后端启动
timeout /t 3 /nobreak >nul

REM 启动前端服务
echo 🎨 启动前端服务...
cd frontend
npm run dev

pause
