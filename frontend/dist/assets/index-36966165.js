var sd=Object.defineProperty;var ld=(e,t,n)=>t in e?sd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Ae=(e,t,n)=>(ld(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const i of l)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(l){const i={};return l.integrity&&(i.integrity=l.integrity),l.referrerPolicy&&(i.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?i.credentials="include":l.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(l){if(l.ep)return;l.ep=!0;const i=n(l);fetch(l.href,i)}})();function id(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var _a={exports:{}},As={},za={exports:{}},R={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vr=Symbol.for("react.element"),od=Symbol.for("react.portal"),ad=Symbol.for("react.fragment"),cd=Symbol.for("react.strict_mode"),ud=Symbol.for("react.profiler"),dd=Symbol.for("react.provider"),md=Symbol.for("react.context"),fd=Symbol.for("react.forward_ref"),pd=Symbol.for("react.suspense"),hd=Symbol.for("react.memo"),gd=Symbol.for("react.lazy"),xo=Symbol.iterator;function xd(e){return e===null||typeof e!="object"?null:(e=xo&&e[xo]||e["@@iterator"],typeof e=="function"?e:null)}var Pa={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ra=Object.assign,$a={};function Mn(e,t,n){this.props=e,this.context=t,this.refs=$a,this.updater=n||Pa}Mn.prototype.isReactComponent={};Mn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Mn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Oa(){}Oa.prototype=Mn.prototype;function wi(e,t,n){this.props=e,this.context=t,this.refs=$a,this.updater=n||Pa}var Ni=wi.prototype=new Oa;Ni.constructor=wi;Ra(Ni,Mn.prototype);Ni.isPureReactComponent=!0;var yo=Array.isArray,Ua=Object.prototype.hasOwnProperty,Si={current:null},Fa={key:!0,ref:!0,__self:!0,__source:!0};function Va(e,t,n){var r,l={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)Ua.call(t,r)&&!Fa.hasOwnProperty(r)&&(l[r]=t[r]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var c=Array(a),f=0;f<a;f++)c[f]=arguments[f+2];l.children=c}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)l[r]===void 0&&(l[r]=a[r]);return{$$typeof:vr,type:e,key:i,ref:o,props:l,_owner:Si.current}}function yd(e,t){return{$$typeof:vr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ki(e){return typeof e=="object"&&e!==null&&e.$$typeof===vr}function vd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var vo=/\/+/g;function Gs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?vd(""+e.key):t.toString(36)}function Br(e,t,n,r,l){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case vr:case od:o=!0}}if(o)return o=e,l=l(o),e=r===""?"."+Gs(o,0):r,yo(l)?(n="",e!=null&&(n=e.replace(vo,"$&/")+"/"),Br(l,t,n,"",function(f){return f})):l!=null&&(ki(l)&&(l=yd(l,n+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(vo,"$&/")+"/")+e)),t.push(l)),1;if(o=0,r=r===""?".":r+":",yo(e))for(var a=0;a<e.length;a++){i=e[a];var c=r+Gs(i,a);o+=Br(i,t,n,c,l)}else if(c=xd(e),typeof c=="function")for(e=c.call(e),a=0;!(i=e.next()).done;)i=i.value,c=r+Gs(i,a++),o+=Br(i,t,n,c,l);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Cr(e,t,n){if(e==null)return e;var r=[],l=0;return Br(e,r,"","",function(i){return t.call(n,i,l++)}),r}function jd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var xe={current:null},Wr={transition:null},wd={ReactCurrentDispatcher:xe,ReactCurrentBatchConfig:Wr,ReactCurrentOwner:Si};function Ha(){throw Error("act(...) is not supported in production builds of React.")}R.Children={map:Cr,forEach:function(e,t,n){Cr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Cr(e,function(){t++}),t},toArray:function(e){return Cr(e,function(t){return t})||[]},only:function(e){if(!ki(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};R.Component=Mn;R.Fragment=ad;R.Profiler=ud;R.PureComponent=wi;R.StrictMode=cd;R.Suspense=pd;R.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=wd;R.act=Ha;R.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ra({},e.props),l=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Si.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)Ua.call(t,c)&&!Fa.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){a=Array(c);for(var f=0;f<c;f++)a[f]=arguments[f+2];r.children=a}return{$$typeof:vr,type:e.type,key:l,ref:i,props:r,_owner:o}};R.createContext=function(e){return e={$$typeof:md,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:dd,_context:e},e.Consumer=e};R.createElement=Va;R.createFactory=function(e){var t=Va.bind(null,e);return t.type=e,t};R.createRef=function(){return{current:null}};R.forwardRef=function(e){return{$$typeof:fd,render:e}};R.isValidElement=ki;R.lazy=function(e){return{$$typeof:gd,_payload:{_status:-1,_result:e},_init:jd}};R.memo=function(e,t){return{$$typeof:hd,type:e,compare:t===void 0?null:t}};R.startTransition=function(e){var t=Wr.transition;Wr.transition={};try{e()}finally{Wr.transition=t}};R.unstable_act=Ha;R.useCallback=function(e,t){return xe.current.useCallback(e,t)};R.useContext=function(e){return xe.current.useContext(e)};R.useDebugValue=function(){};R.useDeferredValue=function(e){return xe.current.useDeferredValue(e)};R.useEffect=function(e,t){return xe.current.useEffect(e,t)};R.useId=function(){return xe.current.useId()};R.useImperativeHandle=function(e,t,n){return xe.current.useImperativeHandle(e,t,n)};R.useInsertionEffect=function(e,t){return xe.current.useInsertionEffect(e,t)};R.useLayoutEffect=function(e,t){return xe.current.useLayoutEffect(e,t)};R.useMemo=function(e,t){return xe.current.useMemo(e,t)};R.useReducer=function(e,t,n){return xe.current.useReducer(e,t,n)};R.useRef=function(e){return xe.current.useRef(e)};R.useState=function(e){return xe.current.useState(e)};R.useSyncExternalStore=function(e,t,n){return xe.current.useSyncExternalStore(e,t,n)};R.useTransition=function(){return xe.current.useTransition()};R.version="18.3.1";za.exports=R;var T=za.exports;const Ba=id(T);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nd=T,Sd=Symbol.for("react.element"),kd=Symbol.for("react.fragment"),bd=Object.prototype.hasOwnProperty,Cd=Nd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ed={key:!0,ref:!0,__self:!0,__source:!0};function Wa(e,t,n){var r,l={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)bd.call(t,r)&&!Ed.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:Sd,type:e,key:i,ref:o,props:l,_owner:Cd.current}}As.Fragment=kd;As.jsx=Wa;As.jsxs=Wa;_a.exports=As;var s=_a.exports,Nl={},Qa={exports:{}},De={},Ka={exports:{}},Ga={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,k){var C=E.length;E.push(k);e:for(;0<C;){var A=C-1>>>1,P=E[A];if(0<l(P,k))E[A]=k,E[C]=P,C=A;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var k=E[0],C=E.pop();if(C!==k){E[0]=C;e:for(var A=0,P=E.length,K=P>>>1;A<K;){var I=2*(A+1)-1,pe=E[I],H=I+1,Pt=E[H];if(0>l(pe,C))H<P&&0>l(Pt,pe)?(E[A]=Pt,E[H]=C,A=H):(E[A]=pe,E[I]=C,A=I);else if(H<P&&0>l(Pt,C))E[A]=Pt,E[H]=C,A=H;else break e}}return k}function l(E,k){var C=E.sortIndex-k.sortIndex;return C!==0?C:E.id-k.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var c=[],f=[],x=1,g=null,y=3,v=!1,w=!1,h=!1,N=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function u(E){for(var k=n(f);k!==null;){if(k.callback===null)r(f);else if(k.startTime<=E)r(f),k.sortIndex=k.expirationTime,t(c,k);else break;k=n(f)}}function p(E){if(h=!1,u(E),!w)if(n(c)!==null)w=!0,zt(j);else{var k=n(f);k!==null&&en(p,k.startTime-E)}}function j(E,k){w=!1,h&&(h=!1,m(M),M=-1),v=!0;var C=y;try{for(u(k),g=n(c);g!==null&&(!(g.expirationTime>k)||E&&!ve());){var A=g.callback;if(typeof A=="function"){g.callback=null,y=g.priorityLevel;var P=A(g.expirationTime<=k);k=e.unstable_now(),typeof P=="function"?g.callback=P:g===n(c)&&r(c),u(k)}else r(c);g=n(c)}if(g!==null)var K=!0;else{var I=n(f);I!==null&&en(p,I.startTime-k),K=!1}return K}finally{g=null,y=C,v=!1}}var S=!1,D=null,M=-1,U=5,z=-1;function ve(){return!(e.unstable_now()-z<U)}function It(){if(D!==null){var E=e.unstable_now();z=E;var k=!0;try{k=D(!0,E)}finally{k?_t():(S=!1,D=null)}}else S=!1}var _t;if(typeof d=="function")_t=function(){d(It)};else if(typeof MessageChannel<"u"){var kr=new MessageChannel,br=kr.port2;kr.port1.onmessage=It,_t=function(){br.postMessage(null)}}else _t=function(){N(It,0)};function zt(E){D=E,S||(S=!0,_t())}function en(E,k){M=N(function(){E(e.unstable_now())},k)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){w||v||(w=!0,zt(j))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):U=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return y},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(E){switch(y){case 1:case 2:case 3:var k=3;break;default:k=y}var C=y;y=k;try{return E()}finally{y=C}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,k){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var C=y;y=E;try{return k()}finally{y=C}},e.unstable_scheduleCallback=function(E,k,C){var A=e.unstable_now();switch(typeof C=="object"&&C!==null?(C=C.delay,C=typeof C=="number"&&0<C?A+C:A):C=A,E){case 1:var P=-1;break;case 2:P=250;break;case 5:P=**********;break;case 4:P=1e4;break;default:P=5e3}return P=C+P,E={id:x++,callback:k,priorityLevel:E,startTime:C,expirationTime:P,sortIndex:-1},C>A?(E.sortIndex=C,t(f,E),n(c)===null&&E===n(f)&&(h?(m(M),M=-1):h=!0,en(p,C-A))):(E.sortIndex=P,t(c,E),w||v||(w=!0,zt(j))),E},e.unstable_shouldYield=ve,e.unstable_wrapCallback=function(E){var k=y;return function(){var C=y;y=k;try{return E.apply(this,arguments)}finally{y=C}}}})(Ga);Ka.exports=Ga;var Ld=Ka.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Td=T,Te=Ld;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var qa=new Set,tr={};function Yt(e,t){Sn(e,t),Sn(e+"Capture",t)}function Sn(e,t){for(tr[e]=t,e=0;e<t.length;e++)qa.add(t[e])}var lt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Sl=Object.prototype.hasOwnProperty,Dd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,jo={},wo={};function Md(e){return Sl.call(wo,e)?!0:Sl.call(jo,e)?!1:Dd.test(e)?wo[e]=!0:(jo[e]=!0,!1)}function Ad(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Id(e,t,n,r){if(t===null||typeof t>"u"||Ad(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ye(e,t,n,r,l,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new ye(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new ye(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new ye(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new ye(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new ye(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new ye(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new ye(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new ye(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new ye(e,5,!1,e.toLowerCase(),null,!1,!1)});var bi=/[\-:]([a-z])/g;function Ci(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(bi,Ci);ce[t]=new ye(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(bi,Ci);ce[t]=new ye(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(bi,Ci);ce[t]=new ye(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new ye(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new ye("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new ye(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ei(e,t,n,r){var l=ce.hasOwnProperty(t)?ce[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Id(t,n,l,r)&&(n=null),r||l===null?Md(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ct=Td.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Er=Symbol.for("react.element"),sn=Symbol.for("react.portal"),ln=Symbol.for("react.fragment"),Li=Symbol.for("react.strict_mode"),kl=Symbol.for("react.profiler"),Xa=Symbol.for("react.provider"),Ya=Symbol.for("react.context"),Ti=Symbol.for("react.forward_ref"),bl=Symbol.for("react.suspense"),Cl=Symbol.for("react.suspense_list"),Di=Symbol.for("react.memo"),dt=Symbol.for("react.lazy"),Za=Symbol.for("react.offscreen"),No=Symbol.iterator;function _n(e){return e===null||typeof e!="object"?null:(e=No&&e[No]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,qs;function Vn(e){if(qs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);qs=t&&t[1]||""}return`
`+qs+e}var Xs=!1;function Ys(e,t){if(!e||Xs)return"";Xs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(f){var r=f}Reflect.construct(e,[],t)}else{try{t.call()}catch(f){r=f}e.call(t.prototype)}else{try{throw Error()}catch(f){r=f}e()}}catch(f){if(f&&r&&typeof f.stack=="string"){for(var l=f.stack.split(`
`),i=r.stack.split(`
`),o=l.length-1,a=i.length-1;1<=o&&0<=a&&l[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(l[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||l[o]!==i[a]){var c=`
`+l[o].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=o&&0<=a);break}}}finally{Xs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Vn(e):""}function _d(e){switch(e.tag){case 5:return Vn(e.type);case 16:return Vn("Lazy");case 13:return Vn("Suspense");case 19:return Vn("SuspenseList");case 0:case 2:case 15:return e=Ys(e.type,!1),e;case 11:return e=Ys(e.type.render,!1),e;case 1:return e=Ys(e.type,!0),e;default:return""}}function El(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ln:return"Fragment";case sn:return"Portal";case kl:return"Profiler";case Li:return"StrictMode";case bl:return"Suspense";case Cl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ya:return(e.displayName||"Context")+".Consumer";case Xa:return(e._context.displayName||"Context")+".Provider";case Ti:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Di:return t=e.displayName||null,t!==null?t:El(e.type)||"Memo";case dt:t=e._payload,e=e._init;try{return El(e(t))}catch{}}return null}function zd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return El(t);case 8:return t===Li?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Et(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ja(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Pd(e){var t=Ja(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Lr(e){e._valueTracker||(e._valueTracker=Pd(e))}function ec(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ja(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function rs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ll(e,t){var n=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function So(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Et(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function tc(e,t){t=t.checked,t!=null&&Ei(e,"checked",t,!1)}function Tl(e,t){tc(e,t);var n=Et(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Dl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Dl(e,t.type,Et(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function ko(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Dl(e,t,n){(t!=="number"||rs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Hn=Array.isArray;function xn(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Et(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Ml(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(b(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function bo(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(b(92));if(Hn(n)){if(1<n.length)throw Error(b(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Et(n)}}function nc(e,t){var n=Et(t.value),r=Et(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Co(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function rc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Al(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?rc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Tr,sc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Tr=Tr||document.createElement("div"),Tr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Tr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function nr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Qn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Rd=["Webkit","ms","Moz","O"];Object.keys(Qn).forEach(function(e){Rd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Qn[t]=Qn[e]})});function lc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Qn.hasOwnProperty(e)&&Qn[e]?(""+t).trim():t+"px"}function ic(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=lc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var $d=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Il(e,t){if(t){if($d[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(b(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(b(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(b(61))}if(t.style!=null&&typeof t.style!="object")throw Error(b(62))}}function _l(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zl=null;function Mi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Pl=null,yn=null,vn=null;function Eo(e){if(e=Nr(e)){if(typeof Pl!="function")throw Error(b(280));var t=e.stateNode;t&&(t=Rs(t),Pl(e.stateNode,e.type,t))}}function oc(e){yn?vn?vn.push(e):vn=[e]:yn=e}function ac(){if(yn){var e=yn,t=vn;if(vn=yn=null,Eo(e),t)for(e=0;e<t.length;e++)Eo(t[e])}}function cc(e,t){return e(t)}function uc(){}var Zs=!1;function dc(e,t,n){if(Zs)return e(t,n);Zs=!0;try{return cc(e,t,n)}finally{Zs=!1,(yn!==null||vn!==null)&&(uc(),ac())}}function rr(e,t){var n=e.stateNode;if(n===null)return null;var r=Rs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(b(231,t,typeof n));return n}var Rl=!1;if(lt)try{var zn={};Object.defineProperty(zn,"passive",{get:function(){Rl=!0}}),window.addEventListener("test",zn,zn),window.removeEventListener("test",zn,zn)}catch{Rl=!1}function Od(e,t,n,r,l,i,o,a,c){var f=Array.prototype.slice.call(arguments,3);try{t.apply(n,f)}catch(x){this.onError(x)}}var Kn=!1,ss=null,ls=!1,$l=null,Ud={onError:function(e){Kn=!0,ss=e}};function Fd(e,t,n,r,l,i,o,a,c){Kn=!1,ss=null,Od.apply(Ud,arguments)}function Vd(e,t,n,r,l,i,o,a,c){if(Fd.apply(this,arguments),Kn){if(Kn){var f=ss;Kn=!1,ss=null}else throw Error(b(198));ls||(ls=!0,$l=f)}}function Zt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function mc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Lo(e){if(Zt(e)!==e)throw Error(b(188))}function Hd(e){var t=e.alternate;if(!t){if(t=Zt(e),t===null)throw Error(b(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var i=l.alternate;if(i===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===i.child){for(i=l.child;i;){if(i===n)return Lo(l),e;if(i===r)return Lo(l),t;i=i.sibling}throw Error(b(188))}if(n.return!==r.return)n=l,r=i;else{for(var o=!1,a=l.child;a;){if(a===n){o=!0,n=l,r=i;break}if(a===r){o=!0,r=l,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=l;break}if(a===r){o=!0,r=i,n=l;break}a=a.sibling}if(!o)throw Error(b(189))}}if(n.alternate!==r)throw Error(b(190))}if(n.tag!==3)throw Error(b(188));return n.stateNode.current===n?e:t}function fc(e){return e=Hd(e),e!==null?pc(e):null}function pc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=pc(e);if(t!==null)return t;e=e.sibling}return null}var hc=Te.unstable_scheduleCallback,To=Te.unstable_cancelCallback,Bd=Te.unstable_shouldYield,Wd=Te.unstable_requestPaint,J=Te.unstable_now,Qd=Te.unstable_getCurrentPriorityLevel,Ai=Te.unstable_ImmediatePriority,gc=Te.unstable_UserBlockingPriority,is=Te.unstable_NormalPriority,Kd=Te.unstable_LowPriority,xc=Te.unstable_IdlePriority,Is=null,Xe=null;function Gd(e){if(Xe&&typeof Xe.onCommitFiberRoot=="function")try{Xe.onCommitFiberRoot(Is,e,void 0,(e.current.flags&128)===128)}catch{}}var Be=Math.clz32?Math.clz32:Yd,qd=Math.log,Xd=Math.LN2;function Yd(e){return e>>>=0,e===0?32:31-(qd(e)/Xd|0)|0}var Dr=64,Mr=4194304;function Bn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function os(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~l;a!==0?r=Bn(a):(i&=o,i!==0&&(r=Bn(i)))}else o=n&~l,o!==0?r=Bn(o):i!==0&&(r=Bn(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,i=t&-t,l>=i||l===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Be(t),l=1<<n,r|=e[n],t&=~l;return r}function Zd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Be(i),a=1<<o,c=l[o];c===-1?(!(a&n)||a&r)&&(l[o]=Zd(a,t)):c<=t&&(e.expiredLanes|=a),i&=~a}}function Ol(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function yc(){var e=Dr;return Dr<<=1,!(Dr&4194240)&&(Dr=64),e}function Js(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function jr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Be(t),e[t]=n}function em(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-Be(n),i=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~i}}function Ii(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Be(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var F=0;function vc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var jc,_i,wc,Nc,Sc,Ul=!1,Ar=[],xt=null,yt=null,vt=null,sr=new Map,lr=new Map,ft=[],tm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Do(e,t){switch(e){case"focusin":case"focusout":xt=null;break;case"dragenter":case"dragleave":yt=null;break;case"mouseover":case"mouseout":vt=null;break;case"pointerover":case"pointerout":sr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":lr.delete(t.pointerId)}}function Pn(e,t,n,r,l,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[l]},t!==null&&(t=Nr(t),t!==null&&_i(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function nm(e,t,n,r,l){switch(t){case"focusin":return xt=Pn(xt,e,t,n,r,l),!0;case"dragenter":return yt=Pn(yt,e,t,n,r,l),!0;case"mouseover":return vt=Pn(vt,e,t,n,r,l),!0;case"pointerover":var i=l.pointerId;return sr.set(i,Pn(sr.get(i)||null,e,t,n,r,l)),!0;case"gotpointercapture":return i=l.pointerId,lr.set(i,Pn(lr.get(i)||null,e,t,n,r,l)),!0}return!1}function kc(e){var t=Ft(e.target);if(t!==null){var n=Zt(t);if(n!==null){if(t=n.tag,t===13){if(t=mc(n),t!==null){e.blockedOn=t,Sc(e.priority,function(){wc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Qr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Fl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);zl=r,n.target.dispatchEvent(r),zl=null}else return t=Nr(n),t!==null&&_i(t),e.blockedOn=n,!1;t.shift()}return!0}function Mo(e,t,n){Qr(e)&&n.delete(t)}function rm(){Ul=!1,xt!==null&&Qr(xt)&&(xt=null),yt!==null&&Qr(yt)&&(yt=null),vt!==null&&Qr(vt)&&(vt=null),sr.forEach(Mo),lr.forEach(Mo)}function Rn(e,t){e.blockedOn===t&&(e.blockedOn=null,Ul||(Ul=!0,Te.unstable_scheduleCallback(Te.unstable_NormalPriority,rm)))}function ir(e){function t(l){return Rn(l,e)}if(0<Ar.length){Rn(Ar[0],e);for(var n=1;n<Ar.length;n++){var r=Ar[n];r.blockedOn===e&&(r.blockedOn=null)}}for(xt!==null&&Rn(xt,e),yt!==null&&Rn(yt,e),vt!==null&&Rn(vt,e),sr.forEach(t),lr.forEach(t),n=0;n<ft.length;n++)r=ft[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ft.length&&(n=ft[0],n.blockedOn===null);)kc(n),n.blockedOn===null&&ft.shift()}var jn=ct.ReactCurrentBatchConfig,as=!0;function sm(e,t,n,r){var l=F,i=jn.transition;jn.transition=null;try{F=1,zi(e,t,n,r)}finally{F=l,jn.transition=i}}function lm(e,t,n,r){var l=F,i=jn.transition;jn.transition=null;try{F=4,zi(e,t,n,r)}finally{F=l,jn.transition=i}}function zi(e,t,n,r){if(as){var l=Fl(e,t,n,r);if(l===null)cl(e,t,r,cs,n),Do(e,r);else if(nm(l,e,t,n,r))r.stopPropagation();else if(Do(e,r),t&4&&-1<tm.indexOf(e)){for(;l!==null;){var i=Nr(l);if(i!==null&&jc(i),i=Fl(e,t,n,r),i===null&&cl(e,t,r,cs,n),i===l)break;l=i}l!==null&&r.stopPropagation()}else cl(e,t,r,null,n)}}var cs=null;function Fl(e,t,n,r){if(cs=null,e=Mi(r),e=Ft(e),e!==null)if(t=Zt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=mc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return cs=e,null}function bc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Qd()){case Ai:return 1;case gc:return 4;case is:case Kd:return 16;case xc:return 536870912;default:return 16}default:return 16}}var ht=null,Pi=null,Kr=null;function Cc(){if(Kr)return Kr;var e,t=Pi,n=t.length,r,l="value"in ht?ht.value:ht.textContent,i=l.length;for(e=0;e<n&&t[e]===l[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===l[i-r];r++);return Kr=l.slice(e,1<r?1-r:void 0)}function Gr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ir(){return!0}function Ao(){return!1}function Me(e){function t(n,r,l,i,o){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ir:Ao,this.isPropagationStopped=Ao,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ir)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ir)},persist:function(){},isPersistent:Ir}),t}var An={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ri=Me(An),wr=Y({},An,{view:0,detail:0}),im=Me(wr),el,tl,$n,_s=Y({},wr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$i,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==$n&&($n&&e.type==="mousemove"?(el=e.screenX-$n.screenX,tl=e.screenY-$n.screenY):tl=el=0,$n=e),el)},movementY:function(e){return"movementY"in e?e.movementY:tl}}),Io=Me(_s),om=Y({},_s,{dataTransfer:0}),am=Me(om),cm=Y({},wr,{relatedTarget:0}),nl=Me(cm),um=Y({},An,{animationName:0,elapsedTime:0,pseudoElement:0}),dm=Me(um),mm=Y({},An,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),fm=Me(mm),pm=Y({},An,{data:0}),_o=Me(pm),hm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},gm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},xm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function ym(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=xm[e])?!!t[e]:!1}function $i(){return ym}var vm=Y({},wr,{key:function(e){if(e.key){var t=hm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Gr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?gm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$i,charCode:function(e){return e.type==="keypress"?Gr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Gr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),jm=Me(vm),wm=Y({},_s,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),zo=Me(wm),Nm=Y({},wr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$i}),Sm=Me(Nm),km=Y({},An,{propertyName:0,elapsedTime:0,pseudoElement:0}),bm=Me(km),Cm=Y({},_s,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Em=Me(Cm),Lm=[9,13,27,32],Oi=lt&&"CompositionEvent"in window,Gn=null;lt&&"documentMode"in document&&(Gn=document.documentMode);var Tm=lt&&"TextEvent"in window&&!Gn,Ec=lt&&(!Oi||Gn&&8<Gn&&11>=Gn),Po=String.fromCharCode(32),Ro=!1;function Lc(e,t){switch(e){case"keyup":return Lm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var on=!1;function Dm(e,t){switch(e){case"compositionend":return Tc(t);case"keypress":return t.which!==32?null:(Ro=!0,Po);case"textInput":return e=t.data,e===Po&&Ro?null:e;default:return null}}function Mm(e,t){if(on)return e==="compositionend"||!Oi&&Lc(e,t)?(e=Cc(),Kr=Pi=ht=null,on=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ec&&t.locale!=="ko"?null:t.data;default:return null}}var Am={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $o(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Am[e.type]:t==="textarea"}function Dc(e,t,n,r){oc(r),t=us(t,"onChange"),0<t.length&&(n=new Ri("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,or=null;function Im(e){Fc(e,0)}function zs(e){var t=un(e);if(ec(t))return e}function _m(e,t){if(e==="change")return t}var Mc=!1;if(lt){var rl;if(lt){var sl="oninput"in document;if(!sl){var Oo=document.createElement("div");Oo.setAttribute("oninput","return;"),sl=typeof Oo.oninput=="function"}rl=sl}else rl=!1;Mc=rl&&(!document.documentMode||9<document.documentMode)}function Uo(){qn&&(qn.detachEvent("onpropertychange",Ac),or=qn=null)}function Ac(e){if(e.propertyName==="value"&&zs(or)){var t=[];Dc(t,or,e,Mi(e)),dc(Im,t)}}function zm(e,t,n){e==="focusin"?(Uo(),qn=t,or=n,qn.attachEvent("onpropertychange",Ac)):e==="focusout"&&Uo()}function Pm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return zs(or)}function Rm(e,t){if(e==="click")return zs(t)}function $m(e,t){if(e==="input"||e==="change")return zs(t)}function Om(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qe=typeof Object.is=="function"?Object.is:Om;function ar(e,t){if(Qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!Sl.call(t,l)||!Qe(e[l],t[l]))return!1}return!0}function Fo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Vo(e,t){var n=Fo(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Fo(n)}}function Ic(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ic(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function _c(){for(var e=window,t=rs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=rs(e.document)}return t}function Ui(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Um(e){var t=_c(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ic(n.ownerDocument.documentElement,n)){if(r!==null&&Ui(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,i=Math.min(r.start,l);r=r.end===void 0?i:Math.min(r.end,l),!e.extend&&i>r&&(l=r,r=i,i=l),l=Vo(n,i);var o=Vo(n,r);l&&o&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Fm=lt&&"documentMode"in document&&11>=document.documentMode,an=null,Vl=null,Xn=null,Hl=!1;function Ho(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Hl||an==null||an!==rs(r)||(r=an,"selectionStart"in r&&Ui(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Xn&&ar(Xn,r)||(Xn=r,r=us(Vl,"onSelect"),0<r.length&&(t=new Ri("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=an)))}function _r(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var cn={animationend:_r("Animation","AnimationEnd"),animationiteration:_r("Animation","AnimationIteration"),animationstart:_r("Animation","AnimationStart"),transitionend:_r("Transition","TransitionEnd")},ll={},zc={};lt&&(zc=document.createElement("div").style,"AnimationEvent"in window||(delete cn.animationend.animation,delete cn.animationiteration.animation,delete cn.animationstart.animation),"TransitionEvent"in window||delete cn.transitionend.transition);function Ps(e){if(ll[e])return ll[e];if(!cn[e])return e;var t=cn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in zc)return ll[e]=t[n];return e}var Pc=Ps("animationend"),Rc=Ps("animationiteration"),$c=Ps("animationstart"),Oc=Ps("transitionend"),Uc=new Map,Bo="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tt(e,t){Uc.set(e,t),Yt(t,[e])}for(var il=0;il<Bo.length;il++){var ol=Bo[il],Vm=ol.toLowerCase(),Hm=ol[0].toUpperCase()+ol.slice(1);Tt(Vm,"on"+Hm)}Tt(Pc,"onAnimationEnd");Tt(Rc,"onAnimationIteration");Tt($c,"onAnimationStart");Tt("dblclick","onDoubleClick");Tt("focusin","onFocus");Tt("focusout","onBlur");Tt(Oc,"onTransitionEnd");Sn("onMouseEnter",["mouseout","mouseover"]);Sn("onMouseLeave",["mouseout","mouseover"]);Sn("onPointerEnter",["pointerout","pointerover"]);Sn("onPointerLeave",["pointerout","pointerover"]);Yt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Yt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Yt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Yt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Yt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Yt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Wn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Bm=new Set("cancel close invalid load scroll toggle".split(" ").concat(Wn));function Wo(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Vd(r,t,void 0,e),e.currentTarget=null}function Fc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],c=a.instance,f=a.currentTarget;if(a=a.listener,c!==i&&l.isPropagationStopped())break e;Wo(l,a,f),i=c}else for(o=0;o<r.length;o++){if(a=r[o],c=a.instance,f=a.currentTarget,a=a.listener,c!==i&&l.isPropagationStopped())break e;Wo(l,a,f),i=c}}}if(ls)throw e=$l,ls=!1,$l=null,e}function W(e,t){var n=t[Gl];n===void 0&&(n=t[Gl]=new Set);var r=e+"__bubble";n.has(r)||(Vc(t,e,2,!1),n.add(r))}function al(e,t,n){var r=0;t&&(r|=4),Vc(n,e,r,t)}var zr="_reactListening"+Math.random().toString(36).slice(2);function cr(e){if(!e[zr]){e[zr]=!0,qa.forEach(function(n){n!=="selectionchange"&&(Bm.has(n)||al(n,!1,e),al(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[zr]||(t[zr]=!0,al("selectionchange",!1,t))}}function Vc(e,t,n,r){switch(bc(t)){case 1:var l=sm;break;case 4:l=lm;break;default:l=zi}n=l.bind(null,t,n,e),l=void 0,!Rl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function cl(e,t,n,r,l){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(o===4)for(o=r.return;o!==null;){var c=o.tag;if((c===3||c===4)&&(c=o.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;o=o.return}for(;a!==null;){if(o=Ft(a),o===null)return;if(c=o.tag,c===5||c===6){r=i=o;continue e}a=a.parentNode}}r=r.return}dc(function(){var f=i,x=Mi(n),g=[];e:{var y=Uc.get(e);if(y!==void 0){var v=Ri,w=e;switch(e){case"keypress":if(Gr(n)===0)break e;case"keydown":case"keyup":v=jm;break;case"focusin":w="focus",v=nl;break;case"focusout":w="blur",v=nl;break;case"beforeblur":case"afterblur":v=nl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=Io;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=am;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=Sm;break;case Pc:case Rc:case $c:v=dm;break;case Oc:v=bm;break;case"scroll":v=im;break;case"wheel":v=Em;break;case"copy":case"cut":case"paste":v=fm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=zo}var h=(t&4)!==0,N=!h&&e==="scroll",m=h?y!==null?y+"Capture":null:y;h=[];for(var d=f,u;d!==null;){u=d;var p=u.stateNode;if(u.tag===5&&p!==null&&(u=p,m!==null&&(p=rr(d,m),p!=null&&h.push(ur(d,p,u)))),N)break;d=d.return}0<h.length&&(y=new v(y,w,null,n,x),g.push({event:y,listeners:h}))}}if(!(t&7)){e:{if(y=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",y&&n!==zl&&(w=n.relatedTarget||n.fromElement)&&(Ft(w)||w[it]))break e;if((v||y)&&(y=x.window===x?x:(y=x.ownerDocument)?y.defaultView||y.parentWindow:window,v?(w=n.relatedTarget||n.toElement,v=f,w=w?Ft(w):null,w!==null&&(N=Zt(w),w!==N||w.tag!==5&&w.tag!==6)&&(w=null)):(v=null,w=f),v!==w)){if(h=Io,p="onMouseLeave",m="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(h=zo,p="onPointerLeave",m="onPointerEnter",d="pointer"),N=v==null?y:un(v),u=w==null?y:un(w),y=new h(p,d+"leave",v,n,x),y.target=N,y.relatedTarget=u,p=null,Ft(x)===f&&(h=new h(m,d+"enter",w,n,x),h.target=u,h.relatedTarget=N,p=h),N=p,v&&w)t:{for(h=v,m=w,d=0,u=h;u;u=rn(u))d++;for(u=0,p=m;p;p=rn(p))u++;for(;0<d-u;)h=rn(h),d--;for(;0<u-d;)m=rn(m),u--;for(;d--;){if(h===m||m!==null&&h===m.alternate)break t;h=rn(h),m=rn(m)}h=null}else h=null;v!==null&&Qo(g,y,v,h,!1),w!==null&&N!==null&&Qo(g,N,w,h,!0)}}e:{if(y=f?un(f):window,v=y.nodeName&&y.nodeName.toLowerCase(),v==="select"||v==="input"&&y.type==="file")var j=_m;else if($o(y))if(Mc)j=$m;else{j=Pm;var S=zm}else(v=y.nodeName)&&v.toLowerCase()==="input"&&(y.type==="checkbox"||y.type==="radio")&&(j=Rm);if(j&&(j=j(e,f))){Dc(g,j,n,x);break e}S&&S(e,y,f),e==="focusout"&&(S=y._wrapperState)&&S.controlled&&y.type==="number"&&Dl(y,"number",y.value)}switch(S=f?un(f):window,e){case"focusin":($o(S)||S.contentEditable==="true")&&(an=S,Vl=f,Xn=null);break;case"focusout":Xn=Vl=an=null;break;case"mousedown":Hl=!0;break;case"contextmenu":case"mouseup":case"dragend":Hl=!1,Ho(g,n,x);break;case"selectionchange":if(Fm)break;case"keydown":case"keyup":Ho(g,n,x)}var D;if(Oi)e:{switch(e){case"compositionstart":var M="onCompositionStart";break e;case"compositionend":M="onCompositionEnd";break e;case"compositionupdate":M="onCompositionUpdate";break e}M=void 0}else on?Lc(e,n)&&(M="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(M="onCompositionStart");M&&(Ec&&n.locale!=="ko"&&(on||M!=="onCompositionStart"?M==="onCompositionEnd"&&on&&(D=Cc()):(ht=x,Pi="value"in ht?ht.value:ht.textContent,on=!0)),S=us(f,M),0<S.length&&(M=new _o(M,e,null,n,x),g.push({event:M,listeners:S}),D?M.data=D:(D=Tc(n),D!==null&&(M.data=D)))),(D=Tm?Dm(e,n):Mm(e,n))&&(f=us(f,"onBeforeInput"),0<f.length&&(x=new _o("onBeforeInput","beforeinput",null,n,x),g.push({event:x,listeners:f}),x.data=D))}Fc(g,t)})}function ur(e,t,n){return{instance:e,listener:t,currentTarget:n}}function us(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,i=l.stateNode;l.tag===5&&i!==null&&(l=i,i=rr(e,n),i!=null&&r.unshift(ur(e,i,l)),i=rr(e,t),i!=null&&r.push(ur(e,i,l))),e=e.return}return r}function rn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Qo(e,t,n,r,l){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,c=a.alternate,f=a.stateNode;if(c!==null&&c===r)break;a.tag===5&&f!==null&&(a=f,l?(c=rr(n,i),c!=null&&o.unshift(ur(n,c,a))):l||(c=rr(n,i),c!=null&&o.push(ur(n,c,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Wm=/\r\n?/g,Qm=/\u0000|\uFFFD/g;function Ko(e){return(typeof e=="string"?e:""+e).replace(Wm,`
`).replace(Qm,"")}function Pr(e,t,n){if(t=Ko(t),Ko(e)!==t&&n)throw Error(b(425))}function ds(){}var Bl=null,Wl=null;function Ql(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Kl=typeof setTimeout=="function"?setTimeout:void 0,Km=typeof clearTimeout=="function"?clearTimeout:void 0,Go=typeof Promise=="function"?Promise:void 0,Gm=typeof queueMicrotask=="function"?queueMicrotask:typeof Go<"u"?function(e){return Go.resolve(null).then(e).catch(qm)}:Kl;function qm(e){setTimeout(function(){throw e})}function ul(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),ir(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);ir(t)}function jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function qo(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var In=Math.random().toString(36).slice(2),qe="__reactFiber$"+In,dr="__reactProps$"+In,it="__reactContainer$"+In,Gl="__reactEvents$"+In,Xm="__reactListeners$"+In,Ym="__reactHandles$"+In;function Ft(e){var t=e[qe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[it]||n[qe]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=qo(e);e!==null;){if(n=e[qe])return n;e=qo(e)}return t}e=n,n=e.parentNode}return null}function Nr(e){return e=e[qe]||e[it],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function un(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(b(33))}function Rs(e){return e[dr]||null}var ql=[],dn=-1;function Dt(e){return{current:e}}function Q(e){0>dn||(e.current=ql[dn],ql[dn]=null,dn--)}function B(e,t){dn++,ql[dn]=e.current,e.current=t}var Lt={},fe=Dt(Lt),Ne=Dt(!1),Qt=Lt;function kn(e,t){var n=e.type.contextTypes;if(!n)return Lt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},i;for(i in n)l[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Se(e){return e=e.childContextTypes,e!=null}function ms(){Q(Ne),Q(fe)}function Xo(e,t,n){if(fe.current!==Lt)throw Error(b(168));B(fe,t),B(Ne,n)}function Hc(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(b(108,zd(e)||"Unknown",l));return Y({},n,r)}function fs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Lt,Qt=fe.current,B(fe,e),B(Ne,Ne.current),!0}function Yo(e,t,n){var r=e.stateNode;if(!r)throw Error(b(169));n?(e=Hc(e,t,Qt),r.__reactInternalMemoizedMergedChildContext=e,Q(Ne),Q(fe),B(fe,e)):Q(Ne),B(Ne,n)}var Je=null,$s=!1,dl=!1;function Bc(e){Je===null?Je=[e]:Je.push(e)}function Zm(e){$s=!0,Bc(e)}function Mt(){if(!dl&&Je!==null){dl=!0;var e=0,t=F;try{var n=Je;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Je=null,$s=!1}catch(l){throw Je!==null&&(Je=Je.slice(e+1)),hc(Ai,Mt),l}finally{F=t,dl=!1}}return null}var mn=[],fn=0,ps=null,hs=0,Ie=[],_e=0,Kt=null,tt=1,nt="";function Rt(e,t){mn[fn++]=hs,mn[fn++]=ps,ps=e,hs=t}function Wc(e,t,n){Ie[_e++]=tt,Ie[_e++]=nt,Ie[_e++]=Kt,Kt=e;var r=tt;e=nt;var l=32-Be(r)-1;r&=~(1<<l),n+=1;var i=32-Be(t)+l;if(30<i){var o=l-l%5;i=(r&(1<<o)-1).toString(32),r>>=o,l-=o,tt=1<<32-Be(t)+l|n<<l|r,nt=i+e}else tt=1<<i|n<<l|r,nt=e}function Fi(e){e.return!==null&&(Rt(e,1),Wc(e,1,0))}function Vi(e){for(;e===ps;)ps=mn[--fn],mn[fn]=null,hs=mn[--fn],mn[fn]=null;for(;e===Kt;)Kt=Ie[--_e],Ie[_e]=null,nt=Ie[--_e],Ie[_e]=null,tt=Ie[--_e],Ie[_e]=null}var Le=null,Ee=null,G=!1,Ve=null;function Qc(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Zo(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Le=e,Ee=jt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Le=e,Ee=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Kt!==null?{id:tt,overflow:nt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Le=e,Ee=null,!0):!1;default:return!1}}function Xl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Yl(e){if(G){var t=Ee;if(t){var n=t;if(!Zo(e,t)){if(Xl(e))throw Error(b(418));t=jt(n.nextSibling);var r=Le;t&&Zo(e,t)?Qc(r,n):(e.flags=e.flags&-4097|2,G=!1,Le=e)}}else{if(Xl(e))throw Error(b(418));e.flags=e.flags&-4097|2,G=!1,Le=e}}}function Jo(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Le=e}function Rr(e){if(e!==Le)return!1;if(!G)return Jo(e),G=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ql(e.type,e.memoizedProps)),t&&(t=Ee)){if(Xl(e))throw Kc(),Error(b(418));for(;t;)Qc(e,t),t=jt(t.nextSibling)}if(Jo(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(b(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ee=jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ee=null}}else Ee=Le?jt(e.stateNode.nextSibling):null;return!0}function Kc(){for(var e=Ee;e;)e=jt(e.nextSibling)}function bn(){Ee=Le=null,G=!1}function Hi(e){Ve===null?Ve=[e]:Ve.push(e)}var Jm=ct.ReactCurrentBatchConfig;function On(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(b(309));var r=n.stateNode}if(!r)throw Error(b(147,e));var l=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=l.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(b(284));if(!n._owner)throw Error(b(290,e))}return e}function $r(e,t){throw e=Object.prototype.toString.call(t),Error(b(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ea(e){var t=e._init;return t(e._payload)}function Gc(e){function t(m,d){if(e){var u=m.deletions;u===null?(m.deletions=[d],m.flags|=16):u.push(d)}}function n(m,d){if(!e)return null;for(;d!==null;)t(m,d),d=d.sibling;return null}function r(m,d){for(m=new Map;d!==null;)d.key!==null?m.set(d.key,d):m.set(d.index,d),d=d.sibling;return m}function l(m,d){return m=kt(m,d),m.index=0,m.sibling=null,m}function i(m,d,u){return m.index=u,e?(u=m.alternate,u!==null?(u=u.index,u<d?(m.flags|=2,d):u):(m.flags|=2,d)):(m.flags|=1048576,d)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,d,u,p){return d===null||d.tag!==6?(d=yl(u,m.mode,p),d.return=m,d):(d=l(d,u),d.return=m,d)}function c(m,d,u,p){var j=u.type;return j===ln?x(m,d,u.props.children,p,u.key):d!==null&&(d.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===dt&&ea(j)===d.type)?(p=l(d,u.props),p.ref=On(m,d,u),p.return=m,p):(p=ts(u.type,u.key,u.props,null,m.mode,p),p.ref=On(m,d,u),p.return=m,p)}function f(m,d,u,p){return d===null||d.tag!==4||d.stateNode.containerInfo!==u.containerInfo||d.stateNode.implementation!==u.implementation?(d=vl(u,m.mode,p),d.return=m,d):(d=l(d,u.children||[]),d.return=m,d)}function x(m,d,u,p,j){return d===null||d.tag!==7?(d=Wt(u,m.mode,p,j),d.return=m,d):(d=l(d,u),d.return=m,d)}function g(m,d,u){if(typeof d=="string"&&d!==""||typeof d=="number")return d=yl(""+d,m.mode,u),d.return=m,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Er:return u=ts(d.type,d.key,d.props,null,m.mode,u),u.ref=On(m,null,d),u.return=m,u;case sn:return d=vl(d,m.mode,u),d.return=m,d;case dt:var p=d._init;return g(m,p(d._payload),u)}if(Hn(d)||_n(d))return d=Wt(d,m.mode,u,null),d.return=m,d;$r(m,d)}return null}function y(m,d,u,p){var j=d!==null?d.key:null;if(typeof u=="string"&&u!==""||typeof u=="number")return j!==null?null:a(m,d,""+u,p);if(typeof u=="object"&&u!==null){switch(u.$$typeof){case Er:return u.key===j?c(m,d,u,p):null;case sn:return u.key===j?f(m,d,u,p):null;case dt:return j=u._init,y(m,d,j(u._payload),p)}if(Hn(u)||_n(u))return j!==null?null:x(m,d,u,p,null);$r(m,u)}return null}function v(m,d,u,p,j){if(typeof p=="string"&&p!==""||typeof p=="number")return m=m.get(u)||null,a(d,m,""+p,j);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Er:return m=m.get(p.key===null?u:p.key)||null,c(d,m,p,j);case sn:return m=m.get(p.key===null?u:p.key)||null,f(d,m,p,j);case dt:var S=p._init;return v(m,d,u,S(p._payload),j)}if(Hn(p)||_n(p))return m=m.get(u)||null,x(d,m,p,j,null);$r(d,p)}return null}function w(m,d,u,p){for(var j=null,S=null,D=d,M=d=0,U=null;D!==null&&M<u.length;M++){D.index>M?(U=D,D=null):U=D.sibling;var z=y(m,D,u[M],p);if(z===null){D===null&&(D=U);break}e&&D&&z.alternate===null&&t(m,D),d=i(z,d,M),S===null?j=z:S.sibling=z,S=z,D=U}if(M===u.length)return n(m,D),G&&Rt(m,M),j;if(D===null){for(;M<u.length;M++)D=g(m,u[M],p),D!==null&&(d=i(D,d,M),S===null?j=D:S.sibling=D,S=D);return G&&Rt(m,M),j}for(D=r(m,D);M<u.length;M++)U=v(D,m,M,u[M],p),U!==null&&(e&&U.alternate!==null&&D.delete(U.key===null?M:U.key),d=i(U,d,M),S===null?j=U:S.sibling=U,S=U);return e&&D.forEach(function(ve){return t(m,ve)}),G&&Rt(m,M),j}function h(m,d,u,p){var j=_n(u);if(typeof j!="function")throw Error(b(150));if(u=j.call(u),u==null)throw Error(b(151));for(var S=j=null,D=d,M=d=0,U=null,z=u.next();D!==null&&!z.done;M++,z=u.next()){D.index>M?(U=D,D=null):U=D.sibling;var ve=y(m,D,z.value,p);if(ve===null){D===null&&(D=U);break}e&&D&&ve.alternate===null&&t(m,D),d=i(ve,d,M),S===null?j=ve:S.sibling=ve,S=ve,D=U}if(z.done)return n(m,D),G&&Rt(m,M),j;if(D===null){for(;!z.done;M++,z=u.next())z=g(m,z.value,p),z!==null&&(d=i(z,d,M),S===null?j=z:S.sibling=z,S=z);return G&&Rt(m,M),j}for(D=r(m,D);!z.done;M++,z=u.next())z=v(D,m,M,z.value,p),z!==null&&(e&&z.alternate!==null&&D.delete(z.key===null?M:z.key),d=i(z,d,M),S===null?j=z:S.sibling=z,S=z);return e&&D.forEach(function(It){return t(m,It)}),G&&Rt(m,M),j}function N(m,d,u,p){if(typeof u=="object"&&u!==null&&u.type===ln&&u.key===null&&(u=u.props.children),typeof u=="object"&&u!==null){switch(u.$$typeof){case Er:e:{for(var j=u.key,S=d;S!==null;){if(S.key===j){if(j=u.type,j===ln){if(S.tag===7){n(m,S.sibling),d=l(S,u.props.children),d.return=m,m=d;break e}}else if(S.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===dt&&ea(j)===S.type){n(m,S.sibling),d=l(S,u.props),d.ref=On(m,S,u),d.return=m,m=d;break e}n(m,S);break}else t(m,S);S=S.sibling}u.type===ln?(d=Wt(u.props.children,m.mode,p,u.key),d.return=m,m=d):(p=ts(u.type,u.key,u.props,null,m.mode,p),p.ref=On(m,d,u),p.return=m,m=p)}return o(m);case sn:e:{for(S=u.key;d!==null;){if(d.key===S)if(d.tag===4&&d.stateNode.containerInfo===u.containerInfo&&d.stateNode.implementation===u.implementation){n(m,d.sibling),d=l(d,u.children||[]),d.return=m,m=d;break e}else{n(m,d);break}else t(m,d);d=d.sibling}d=vl(u,m.mode,p),d.return=m,m=d}return o(m);case dt:return S=u._init,N(m,d,S(u._payload),p)}if(Hn(u))return w(m,d,u,p);if(_n(u))return h(m,d,u,p);$r(m,u)}return typeof u=="string"&&u!==""||typeof u=="number"?(u=""+u,d!==null&&d.tag===6?(n(m,d.sibling),d=l(d,u),d.return=m,m=d):(n(m,d),d=yl(u,m.mode,p),d.return=m,m=d),o(m)):n(m,d)}return N}var Cn=Gc(!0),qc=Gc(!1),gs=Dt(null),xs=null,pn=null,Bi=null;function Wi(){Bi=pn=xs=null}function Qi(e){var t=gs.current;Q(gs),e._currentValue=t}function Zl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function wn(e,t){xs=e,Bi=pn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(we=!0),e.firstContext=null)}function Re(e){var t=e._currentValue;if(Bi!==e)if(e={context:e,memoizedValue:t,next:null},pn===null){if(xs===null)throw Error(b(308));pn=e,xs.dependencies={lanes:0,firstContext:e}}else pn=pn.next=e;return t}var Vt=null;function Ki(e){Vt===null?Vt=[e]:Vt.push(e)}function Xc(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Ki(t)):(n.next=l.next,l.next=n),t.interleaved=n,ot(e,r)}function ot(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var mt=!1;function Gi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Yc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function st(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function wt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,$&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,ot(e,n)}return l=r.interleaved,l===null?(t.next=t,Ki(r)):(t.next=l.next,l.next=t),r.interleaved=t,ot(e,n)}function qr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ii(e,n)}}function ta(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?l=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?l=i=t:i=i.next=t}else l=i=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ys(e,t,n,r){var l=e.updateQueue;mt=!1;var i=l.firstBaseUpdate,o=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var c=a,f=c.next;c.next=null,o===null?i=f:o.next=f,o=c;var x=e.alternate;x!==null&&(x=x.updateQueue,a=x.lastBaseUpdate,a!==o&&(a===null?x.firstBaseUpdate=f:a.next=f,x.lastBaseUpdate=c))}if(i!==null){var g=l.baseState;o=0,x=f=c=null,a=i;do{var y=a.lane,v=a.eventTime;if((r&y)===y){x!==null&&(x=x.next={eventTime:v,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var w=e,h=a;switch(y=t,v=n,h.tag){case 1:if(w=h.payload,typeof w=="function"){g=w.call(v,g,y);break e}g=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=h.payload,y=typeof w=="function"?w.call(v,g,y):w,y==null)break e;g=Y({},g,y);break e;case 2:mt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,y=l.effects,y===null?l.effects=[a]:y.push(a))}else v={eventTime:v,lane:y,tag:a.tag,payload:a.payload,callback:a.callback,next:null},x===null?(f=x=v,c=g):x=x.next=v,o|=y;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;y=a,a=y.next,y.next=null,l.lastBaseUpdate=y,l.shared.pending=null}}while(1);if(x===null&&(c=g),l.baseState=c,l.firstBaseUpdate=f,l.lastBaseUpdate=x,t=l.shared.interleaved,t!==null){l=t;do o|=l.lane,l=l.next;while(l!==t)}else i===null&&(l.shared.lanes=0);qt|=o,e.lanes=o,e.memoizedState=g}}function na(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(b(191,l));l.call(r)}}}var Sr={},Ye=Dt(Sr),mr=Dt(Sr),fr=Dt(Sr);function Ht(e){if(e===Sr)throw Error(b(174));return e}function qi(e,t){switch(B(fr,t),B(mr,e),B(Ye,Sr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Al(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Al(t,e)}Q(Ye),B(Ye,t)}function En(){Q(Ye),Q(mr),Q(fr)}function Zc(e){Ht(fr.current);var t=Ht(Ye.current),n=Al(t,e.type);t!==n&&(B(mr,e),B(Ye,n))}function Xi(e){mr.current===e&&(Q(Ye),Q(mr))}var q=Dt(0);function vs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ml=[];function Yi(){for(var e=0;e<ml.length;e++)ml[e]._workInProgressVersionPrimary=null;ml.length=0}var Xr=ct.ReactCurrentDispatcher,fl=ct.ReactCurrentBatchConfig,Gt=0,X=null,te=null,se=null,js=!1,Yn=!1,pr=0,ef=0;function ue(){throw Error(b(321))}function Zi(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qe(e[n],t[n]))return!1;return!0}function Ji(e,t,n,r,l,i){if(Gt=i,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Xr.current=e===null||e.memoizedState===null?sf:lf,e=n(r,l),Yn){i=0;do{if(Yn=!1,pr=0,25<=i)throw Error(b(301));i+=1,se=te=null,t.updateQueue=null,Xr.current=of,e=n(r,l)}while(Yn)}if(Xr.current=ws,t=te!==null&&te.next!==null,Gt=0,se=te=X=null,js=!1,t)throw Error(b(300));return e}function eo(){var e=pr!==0;return pr=0,e}function Ge(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?X.memoizedState=se=e:se=se.next=e,se}function $e(){if(te===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=se===null?X.memoizedState:se.next;if(t!==null)se=t,te=e;else{if(e===null)throw Error(b(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},se===null?X.memoizedState=se=e:se=se.next=e}return se}function hr(e,t){return typeof t=="function"?t(e):t}function pl(e){var t=$e(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=te,l=r.baseQueue,i=n.pending;if(i!==null){if(l!==null){var o=l.next;l.next=i.next,i.next=o}r.baseQueue=l=i,n.pending=null}if(l!==null){i=l.next,r=r.baseState;var a=o=null,c=null,f=i;do{var x=f.lane;if((Gt&x)===x)c!==null&&(c=c.next={lane:0,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null}),r=f.hasEagerState?f.eagerState:e(r,f.action);else{var g={lane:x,action:f.action,hasEagerState:f.hasEagerState,eagerState:f.eagerState,next:null};c===null?(a=c=g,o=r):c=c.next=g,X.lanes|=x,qt|=x}f=f.next}while(f!==null&&f!==i);c===null?o=r:c.next=a,Qe(r,t.memoizedState)||(we=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do i=l.lane,X.lanes|=i,qt|=i,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function hl(e){var t=$e(),n=t.queue;if(n===null)throw Error(b(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,i=t.memoizedState;if(l!==null){n.pending=null;var o=l=l.next;do i=e(i,o.action),o=o.next;while(o!==l);Qe(i,t.memoizedState)||(we=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Jc(){}function eu(e,t){var n=X,r=$e(),l=t(),i=!Qe(r.memoizedState,l);if(i&&(r.memoizedState=l,we=!0),r=r.queue,to(ru.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,gr(9,nu.bind(null,n,r,l,t),void 0,null),le===null)throw Error(b(349));Gt&30||tu(n,t,l)}return l}function tu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function nu(e,t,n,r){t.value=n,t.getSnapshot=r,su(t)&&lu(e)}function ru(e,t,n){return n(function(){su(t)&&lu(e)})}function su(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qe(e,n)}catch{return!0}}function lu(e){var t=ot(e,1);t!==null&&We(t,e,1,-1)}function ra(e){var t=Ge();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:hr,lastRenderedState:e},t.queue=e,e=e.dispatch=rf.bind(null,X,e),[t.memoizedState,e]}function gr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function iu(){return $e().memoizedState}function Yr(e,t,n,r){var l=Ge();X.flags|=e,l.memoizedState=gr(1|t,n,void 0,r===void 0?null:r)}function Os(e,t,n,r){var l=$e();r=r===void 0?null:r;var i=void 0;if(te!==null){var o=te.memoizedState;if(i=o.destroy,r!==null&&Zi(r,o.deps)){l.memoizedState=gr(t,n,i,r);return}}X.flags|=e,l.memoizedState=gr(1|t,n,i,r)}function sa(e,t){return Yr(8390656,8,e,t)}function to(e,t){return Os(2048,8,e,t)}function ou(e,t){return Os(4,2,e,t)}function au(e,t){return Os(4,4,e,t)}function cu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function uu(e,t,n){return n=n!=null?n.concat([e]):null,Os(4,4,cu.bind(null,t,e),n)}function no(){}function du(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Zi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function mu(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Zi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function fu(e,t,n){return Gt&21?(Qe(n,t)||(n=yc(),X.lanes|=n,qt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,we=!0),e.memoizedState=n)}function tf(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=fl.transition;fl.transition={};try{e(!1),t()}finally{F=n,fl.transition=r}}function pu(){return $e().memoizedState}function nf(e,t,n){var r=St(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},hu(e))gu(t,n);else if(n=Xc(e,t,n,r),n!==null){var l=ge();We(n,e,r,l),xu(n,t,r)}}function rf(e,t,n){var r=St(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(hu(e))gu(t,l);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(l.hasEagerState=!0,l.eagerState=a,Qe(a,o)){var c=t.interleaved;c===null?(l.next=l,Ki(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}n=Xc(e,t,l,r),n!==null&&(l=ge(),We(n,e,r,l),xu(n,t,r))}}function hu(e){var t=e.alternate;return e===X||t!==null&&t===X}function gu(e,t){Yn=js=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function xu(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ii(e,n)}}var ws={readContext:Re,useCallback:ue,useContext:ue,useEffect:ue,useImperativeHandle:ue,useInsertionEffect:ue,useLayoutEffect:ue,useMemo:ue,useReducer:ue,useRef:ue,useState:ue,useDebugValue:ue,useDeferredValue:ue,useTransition:ue,useMutableSource:ue,useSyncExternalStore:ue,useId:ue,unstable_isNewReconciler:!1},sf={readContext:Re,useCallback:function(e,t){return Ge().memoizedState=[e,t===void 0?null:t],e},useContext:Re,useEffect:sa,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Yr(4194308,4,cu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Yr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Yr(4,2,e,t)},useMemo:function(e,t){var n=Ge();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ge();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=nf.bind(null,X,e),[r.memoizedState,e]},useRef:function(e){var t=Ge();return e={current:e},t.memoizedState=e},useState:ra,useDebugValue:no,useDeferredValue:function(e){return Ge().memoizedState=e},useTransition:function(){var e=ra(!1),t=e[0];return e=tf.bind(null,e[1]),Ge().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=X,l=Ge();if(G){if(n===void 0)throw Error(b(407));n=n()}else{if(n=t(),le===null)throw Error(b(349));Gt&30||tu(r,t,n)}l.memoizedState=n;var i={value:n,getSnapshot:t};return l.queue=i,sa(ru.bind(null,r,i,e),[e]),r.flags|=2048,gr(9,nu.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Ge(),t=le.identifierPrefix;if(G){var n=nt,r=tt;n=(r&~(1<<32-Be(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=pr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=ef++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},lf={readContext:Re,useCallback:du,useContext:Re,useEffect:to,useImperativeHandle:uu,useInsertionEffect:ou,useLayoutEffect:au,useMemo:mu,useReducer:pl,useRef:iu,useState:function(){return pl(hr)},useDebugValue:no,useDeferredValue:function(e){var t=$e();return fu(t,te.memoizedState,e)},useTransition:function(){var e=pl(hr)[0],t=$e().memoizedState;return[e,t]},useMutableSource:Jc,useSyncExternalStore:eu,useId:pu,unstable_isNewReconciler:!1},of={readContext:Re,useCallback:du,useContext:Re,useEffect:to,useImperativeHandle:uu,useInsertionEffect:ou,useLayoutEffect:au,useMemo:mu,useReducer:hl,useRef:iu,useState:function(){return hl(hr)},useDebugValue:no,useDeferredValue:function(e){var t=$e();return te===null?t.memoizedState=e:fu(t,te.memoizedState,e)},useTransition:function(){var e=hl(hr)[0],t=$e().memoizedState;return[e,t]},useMutableSource:Jc,useSyncExternalStore:eu,useId:pu,unstable_isNewReconciler:!1};function Ue(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Jl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Us={isMounted:function(e){return(e=e._reactInternals)?Zt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ge(),l=St(e),i=st(r,l);i.payload=t,n!=null&&(i.callback=n),t=wt(e,i,l),t!==null&&(We(t,e,l,r),qr(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ge(),l=St(e),i=st(r,l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=wt(e,i,l),t!==null&&(We(t,e,l,r),qr(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ge(),r=St(e),l=st(n,r);l.tag=2,t!=null&&(l.callback=t),t=wt(e,l,r),t!==null&&(We(t,e,r,n),qr(t,e,r))}};function la(e,t,n,r,l,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!ar(n,r)||!ar(l,i):!0}function yu(e,t,n){var r=!1,l=Lt,i=t.contextType;return typeof i=="object"&&i!==null?i=Re(i):(l=Se(t)?Qt:fe.current,r=t.contextTypes,i=(r=r!=null)?kn(e,l):Lt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Us,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=i),t}function ia(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Us.enqueueReplaceState(t,t.state,null)}function ei(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Gi(e);var i=t.contextType;typeof i=="object"&&i!==null?l.context=Re(i):(i=Se(t)?Qt:fe.current,l.context=kn(e,i)),l.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Jl(e,t,i,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Us.enqueueReplaceState(l,l.state,null),ys(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Ln(e,t){try{var n="",r=t;do n+=_d(r),r=r.return;while(r);var l=n}catch(i){l=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:l,digest:null}}function gl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ti(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var af=typeof WeakMap=="function"?WeakMap:Map;function vu(e,t,n){n=st(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ss||(Ss=!0,di=r),ti(e,t)},n}function ju(e,t,n){n=st(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){ti(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){ti(e,t),typeof r!="function"&&(Nt===null?Nt=new Set([this]):Nt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function oa(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new af;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Nf.bind(null,e,t,n),t.then(e,e))}function aa(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ca(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=st(-1,1),t.tag=2,wt(n,t,1))),n.lanes|=1),e)}var cf=ct.ReactCurrentOwner,we=!1;function he(e,t,n,r){t.child=e===null?qc(t,null,n,r):Cn(t,e.child,n,r)}function ua(e,t,n,r,l){n=n.render;var i=t.ref;return wn(t,l),r=Ji(e,t,n,r,i,l),n=eo(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,at(e,t,l)):(G&&n&&Fi(t),t.flags|=1,he(e,t,r,l),t.child)}function da(e,t,n,r,l){if(e===null){var i=n.type;return typeof i=="function"&&!uo(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,wu(e,t,i,r,l)):(e=ts(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&l)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:ar,n(o,r)&&e.ref===t.ref)return at(e,t,l)}return t.flags|=1,e=kt(i,r),e.ref=t.ref,e.return=t,t.child=e}function wu(e,t,n,r,l){if(e!==null){var i=e.memoizedProps;if(ar(i,r)&&e.ref===t.ref)if(we=!1,t.pendingProps=r=i,(e.lanes&l)!==0)e.flags&131072&&(we=!0);else return t.lanes=e.lanes,at(e,t,l)}return ni(e,t,n,r,l)}function Nu(e,t,n){var r=t.pendingProps,l=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(gn,Ce),Ce|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(gn,Ce),Ce|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,B(gn,Ce),Ce|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,B(gn,Ce),Ce|=r;return he(e,t,l,n),t.child}function Su(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ni(e,t,n,r,l){var i=Se(n)?Qt:fe.current;return i=kn(t,i),wn(t,l),n=Ji(e,t,n,r,i,l),r=eo(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,at(e,t,l)):(G&&r&&Fi(t),t.flags|=1,he(e,t,n,l),t.child)}function ma(e,t,n,r,l){if(Se(n)){var i=!0;fs(t)}else i=!1;if(wn(t,l),t.stateNode===null)Zr(e,t),yu(t,n,r),ei(t,n,r,l),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var c=o.context,f=n.contextType;typeof f=="object"&&f!==null?f=Re(f):(f=Se(n)?Qt:fe.current,f=kn(t,f));var x=n.getDerivedStateFromProps,g=typeof x=="function"||typeof o.getSnapshotBeforeUpdate=="function";g||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||c!==f)&&ia(t,o,r,f),mt=!1;var y=t.memoizedState;o.state=y,ys(t,r,o,l),c=t.memoizedState,a!==r||y!==c||Ne.current||mt?(typeof x=="function"&&(Jl(t,n,x,r),c=t.memoizedState),(a=mt||la(t,n,a,r,y,c,f))?(g||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),o.props=r,o.state=c,o.context=f,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Yc(e,t),a=t.memoizedProps,f=t.type===t.elementType?a:Ue(t.type,a),o.props=f,g=t.pendingProps,y=o.context,c=n.contextType,typeof c=="object"&&c!==null?c=Re(c):(c=Se(n)?Qt:fe.current,c=kn(t,c));var v=n.getDerivedStateFromProps;(x=typeof v=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==g||y!==c)&&ia(t,o,r,c),mt=!1,y=t.memoizedState,o.state=y,ys(t,r,o,l);var w=t.memoizedState;a!==g||y!==w||Ne.current||mt?(typeof v=="function"&&(Jl(t,n,v,r),w=t.memoizedState),(f=mt||la(t,n,f,r,y,w,c)||!1)?(x||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,w,c),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,w,c)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),o.props=r,o.state=w,o.context=c,r=f):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),r=!1)}return ri(e,t,n,r,i,l)}function ri(e,t,n,r,l,i){Su(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return l&&Yo(t,n,!1),at(e,t,i);r=t.stateNode,cf.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Cn(t,e.child,null,i),t.child=Cn(t,null,a,i)):he(e,t,a,i),t.memoizedState=r.state,l&&Yo(t,n,!0),t.child}function ku(e){var t=e.stateNode;t.pendingContext?Xo(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Xo(e,t.context,!1),qi(e,t.containerInfo)}function fa(e,t,n,r,l){return bn(),Hi(l),t.flags|=256,he(e,t,n,r),t.child}var si={dehydrated:null,treeContext:null,retryLane:0};function li(e){return{baseLanes:e,cachePool:null,transitions:null}}function bu(e,t,n){var r=t.pendingProps,l=q.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),B(q,l&1),e===null)return Yl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=Hs(o,r,0,null),e=Wt(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=li(n),t.memoizedState=si,e):ro(t,o));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return uf(e,t,o,r,a,l,n);if(i){i=r.fallback,o=t.mode,l=e.child,a=l.sibling;var c={mode:"hidden",children:r.children};return!(o&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=kt(l,c),r.subtreeFlags=l.subtreeFlags&14680064),a!==null?i=kt(a,i):(i=Wt(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?li(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=si,r}return i=e.child,e=i.sibling,r=kt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ro(e,t){return t=Hs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Or(e,t,n,r){return r!==null&&Hi(r),Cn(t,e.child,null,n),e=ro(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function uf(e,t,n,r,l,i,o){if(n)return t.flags&256?(t.flags&=-257,r=gl(Error(b(422))),Or(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,l=t.mode,r=Hs({mode:"visible",children:r.children},l,0,null),i=Wt(i,l,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Cn(t,e.child,null,o),t.child.memoizedState=li(o),t.memoizedState=si,i);if(!(t.mode&1))return Or(e,t,o,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(b(419)),r=gl(i,r,void 0),Or(e,t,o,r)}if(a=(o&e.childLanes)!==0,we||a){if(r=le,r!==null){switch(o&-o){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|o)?0:l,l!==0&&l!==i.retryLane&&(i.retryLane=l,ot(e,l),We(r,e,l,-1))}return co(),r=gl(Error(b(421))),Or(e,t,o,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=Sf.bind(null,e),l._reactRetry=t,null):(e=i.treeContext,Ee=jt(l.nextSibling),Le=t,G=!0,Ve=null,e!==null&&(Ie[_e++]=tt,Ie[_e++]=nt,Ie[_e++]=Kt,tt=e.id,nt=e.overflow,Kt=t),t=ro(t,r.children),t.flags|=4096,t)}function pa(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Zl(e.return,t,n)}function xl(e,t,n,r,l){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=l)}function Cu(e,t,n){var r=t.pendingProps,l=r.revealOrder,i=r.tail;if(he(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&pa(e,n,t);else if(e.tag===19)pa(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(q,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&vs(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),xl(t,!1,l,n,i);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&vs(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}xl(t,!0,n,null,i);break;case"together":xl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Zr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function at(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),qt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(b(153));if(t.child!==null){for(e=t.child,n=kt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=kt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function df(e,t,n){switch(t.tag){case 3:ku(t),bn();break;case 5:Zc(t);break;case 1:Se(t.type)&&fs(t);break;case 4:qi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;B(gs,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?bu(e,t,n):(B(q,q.current&1),e=at(e,t,n),e!==null?e.sibling:null);B(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Cu(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),B(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,Nu(e,t,n)}return at(e,t,n)}var Eu,ii,Lu,Tu;Eu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ii=function(){};Lu=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Ht(Ye.current);var i=null;switch(n){case"input":l=Ll(e,l),r=Ll(e,r),i=[];break;case"select":l=Y({},l,{value:void 0}),r=Y({},r,{value:void 0}),i=[];break;case"textarea":l=Ml(e,l),r=Ml(e,r),i=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ds)}Il(n,r);var o;n=null;for(f in l)if(!r.hasOwnProperty(f)&&l.hasOwnProperty(f)&&l[f]!=null)if(f==="style"){var a=l[f];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else f!=="dangerouslySetInnerHTML"&&f!=="children"&&f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&f!=="autoFocus"&&(tr.hasOwnProperty(f)?i||(i=[]):(i=i||[]).push(f,null));for(f in r){var c=r[f];if(a=l!=null?l[f]:void 0,r.hasOwnProperty(f)&&c!==a&&(c!=null||a!=null))if(f==="style")if(a){for(o in a)!a.hasOwnProperty(o)||c&&c.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in c)c.hasOwnProperty(o)&&a[o]!==c[o]&&(n||(n={}),n[o]=c[o])}else n||(i||(i=[]),i.push(f,n)),n=c;else f==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(i=i||[]).push(f,c)):f==="children"?typeof c!="string"&&typeof c!="number"||(i=i||[]).push(f,""+c):f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&(tr.hasOwnProperty(f)?(c!=null&&f==="onScroll"&&W("scroll",e),i||a===c||(i=[])):(i=i||[]).push(f,c))}n&&(i=i||[]).push("style",n);var f=i;(t.updateQueue=f)&&(t.flags|=4)}};Tu=function(e,t,n,r){n!==r&&(t.flags|=4)};function Un(e,t){if(!G)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function mf(e,t,n){var r=t.pendingProps;switch(Vi(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Se(t.type)&&ms(),de(t),null;case 3:return r=t.stateNode,En(),Q(Ne),Q(fe),Yi(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Rr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ve!==null&&(pi(Ve),Ve=null))),ii(e,t),de(t),null;case 5:Xi(t);var l=Ht(fr.current);if(n=t.type,e!==null&&t.stateNode!=null)Lu(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(b(166));return de(t),null}if(e=Ht(Ye.current),Rr(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[qe]=t,r[dr]=i,e=(t.mode&1)!==0,n){case"dialog":W("cancel",r),W("close",r);break;case"iframe":case"object":case"embed":W("load",r);break;case"video":case"audio":for(l=0;l<Wn.length;l++)W(Wn[l],r);break;case"source":W("error",r);break;case"img":case"image":case"link":W("error",r),W("load",r);break;case"details":W("toggle",r);break;case"input":So(r,i),W("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},W("invalid",r);break;case"textarea":bo(r,i),W("invalid",r)}Il(n,i),l=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Pr(r.textContent,a,e),l=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Pr(r.textContent,a,e),l=["children",""+a]):tr.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&W("scroll",r)}switch(n){case"input":Lr(r),ko(r,i,!0);break;case"textarea":Lr(r),Co(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=ds)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=rc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[qe]=t,e[dr]=r,Eu(e,t,!1,!1),t.stateNode=e;e:{switch(o=_l(n,r),n){case"dialog":W("cancel",e),W("close",e),l=r;break;case"iframe":case"object":case"embed":W("load",e),l=r;break;case"video":case"audio":for(l=0;l<Wn.length;l++)W(Wn[l],e);l=r;break;case"source":W("error",e),l=r;break;case"img":case"image":case"link":W("error",e),W("load",e),l=r;break;case"details":W("toggle",e),l=r;break;case"input":So(e,r),l=Ll(e,r),W("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=Y({},r,{value:void 0}),W("invalid",e);break;case"textarea":bo(e,r),l=Ml(e,r),W("invalid",e);break;default:l=r}Il(n,l),a=l;for(i in a)if(a.hasOwnProperty(i)){var c=a[i];i==="style"?ic(e,c):i==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&sc(e,c)):i==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&nr(e,c):typeof c=="number"&&nr(e,""+c):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(tr.hasOwnProperty(i)?c!=null&&i==="onScroll"&&W("scroll",e):c!=null&&Ei(e,i,c,o))}switch(n){case"input":Lr(e),ko(e,r,!1);break;case"textarea":Lr(e),Co(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Et(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?xn(e,!!r.multiple,i,!1):r.defaultValue!=null&&xn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=ds)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)Tu(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(b(166));if(n=Ht(fr.current),Ht(Ye.current),Rr(t)){if(r=t.stateNode,n=t.memoizedProps,r[qe]=t,(i=r.nodeValue!==n)&&(e=Le,e!==null))switch(e.tag){case 3:Pr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Pr(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[qe]=t,t.stateNode=r}return de(t),null;case 13:if(Q(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(G&&Ee!==null&&t.mode&1&&!(t.flags&128))Kc(),bn(),t.flags|=98560,i=!1;else if(i=Rr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(b(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(b(317));i[qe]=t}else bn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),i=!1}else Ve!==null&&(pi(Ve),Ve=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?ne===0&&(ne=3):co())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return En(),ii(e,t),e===null&&cr(t.stateNode.containerInfo),de(t),null;case 10:return Qi(t.type._context),de(t),null;case 17:return Se(t.type)&&ms(),de(t),null;case 19:if(Q(q),i=t.memoizedState,i===null)return de(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)Un(i,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=vs(e),o!==null){for(t.flags|=128,Un(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(q,q.current&1|2),t.child}e=e.sibling}i.tail!==null&&J()>Tn&&(t.flags|=128,r=!0,Un(i,!1),t.lanes=4194304)}else{if(!r)if(e=vs(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Un(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!G)return de(t),null}else 2*J()-i.renderingStartTime>Tn&&n!==1073741824&&(t.flags|=128,r=!0,Un(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=J(),t.sibling=null,n=q.current,B(q,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return ao(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ce&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(b(156,t.tag))}function ff(e,t){switch(Vi(t),t.tag){case 1:return Se(t.type)&&ms(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return En(),Q(Ne),Q(fe),Yi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Xi(t),null;case 13:if(Q(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(b(340));bn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Q(q),null;case 4:return En(),null;case 10:return Qi(t.type._context),null;case 22:case 23:return ao(),null;case 24:return null;default:return null}}var Ur=!1,me=!1,pf=typeof WeakSet=="function"?WeakSet:Set,L=null;function hn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function oi(e,t,n){try{n()}catch(r){Z(e,t,r)}}var ha=!1;function hf(e,t){if(Bl=as,e=_c(),Ui(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,c=-1,f=0,x=0,g=e,y=null;t:for(;;){for(var v;g!==n||l!==0&&g.nodeType!==3||(a=o+l),g!==i||r!==0&&g.nodeType!==3||(c=o+r),g.nodeType===3&&(o+=g.nodeValue.length),(v=g.firstChild)!==null;)y=g,g=v;for(;;){if(g===e)break t;if(y===n&&++f===l&&(a=o),y===i&&++x===r&&(c=o),(v=g.nextSibling)!==null)break;g=y,y=g.parentNode}g=v}n=a===-1||c===-1?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Wl={focusedElem:e,selectionRange:n},as=!1,L=t;L!==null;)if(t=L,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,L=e;else for(;L!==null;){t=L;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var h=w.memoizedProps,N=w.memoizedState,m=t.stateNode,d=m.getSnapshotBeforeUpdate(t.elementType===t.type?h:Ue(t.type,h),N);m.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var u=t.stateNode.containerInfo;u.nodeType===1?u.textContent="":u.nodeType===9&&u.documentElement&&u.removeChild(u.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(b(163))}}catch(p){Z(t,t.return,p)}if(e=t.sibling,e!==null){e.return=t.return,L=e;break}L=t.return}return w=ha,ha=!1,w}function Zn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var i=l.destroy;l.destroy=void 0,i!==void 0&&oi(t,n,i)}l=l.next}while(l!==r)}}function Fs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ai(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Du(e){var t=e.alternate;t!==null&&(e.alternate=null,Du(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[qe],delete t[dr],delete t[Gl],delete t[Xm],delete t[Ym])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Mu(e){return e.tag===5||e.tag===3||e.tag===4}function ga(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Mu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ci(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ds));else if(r!==4&&(e=e.child,e!==null))for(ci(e,t,n),e=e.sibling;e!==null;)ci(e,t,n),e=e.sibling}function ui(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ui(e,t,n),e=e.sibling;e!==null;)ui(e,t,n),e=e.sibling}var oe=null,Fe=!1;function ut(e,t,n){for(n=n.child;n!==null;)Au(e,t,n),n=n.sibling}function Au(e,t,n){if(Xe&&typeof Xe.onCommitFiberUnmount=="function")try{Xe.onCommitFiberUnmount(Is,n)}catch{}switch(n.tag){case 5:me||hn(n,t);case 6:var r=oe,l=Fe;oe=null,ut(e,t,n),oe=r,Fe=l,oe!==null&&(Fe?(e=oe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):oe.removeChild(n.stateNode));break;case 18:oe!==null&&(Fe?(e=oe,n=n.stateNode,e.nodeType===8?ul(e.parentNode,n):e.nodeType===1&&ul(e,n),ir(e)):ul(oe,n.stateNode));break;case 4:r=oe,l=Fe,oe=n.stateNode.containerInfo,Fe=!0,ut(e,t,n),oe=r,Fe=l;break;case 0:case 11:case 14:case 15:if(!me&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var i=l,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&oi(n,t,o),l=l.next}while(l!==r)}ut(e,t,n);break;case 1:if(!me&&(hn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Z(n,t,a)}ut(e,t,n);break;case 21:ut(e,t,n);break;case 22:n.mode&1?(me=(r=me)||n.memoizedState!==null,ut(e,t,n),me=r):ut(e,t,n);break;default:ut(e,t,n)}}function xa(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new pf),t.forEach(function(r){var l=kf.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function Oe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:oe=a.stateNode,Fe=!1;break e;case 3:oe=a.stateNode.containerInfo,Fe=!0;break e;case 4:oe=a.stateNode.containerInfo,Fe=!0;break e}a=a.return}if(oe===null)throw Error(b(160));Au(i,o,l),oe=null,Fe=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(f){Z(l,t,f)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Iu(t,e),t=t.sibling}function Iu(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Oe(t,e),Ke(e),r&4){try{Zn(3,e,e.return),Fs(3,e)}catch(h){Z(e,e.return,h)}try{Zn(5,e,e.return)}catch(h){Z(e,e.return,h)}}break;case 1:Oe(t,e),Ke(e),r&512&&n!==null&&hn(n,n.return);break;case 5:if(Oe(t,e),Ke(e),r&512&&n!==null&&hn(n,n.return),e.flags&32){var l=e.stateNode;try{nr(l,"")}catch(h){Z(e,e.return,h)}}if(r&4&&(l=e.stateNode,l!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&tc(l,i),_l(a,o);var f=_l(a,i);for(o=0;o<c.length;o+=2){var x=c[o],g=c[o+1];x==="style"?ic(l,g):x==="dangerouslySetInnerHTML"?sc(l,g):x==="children"?nr(l,g):Ei(l,x,g,f)}switch(a){case"input":Tl(l,i);break;case"textarea":nc(l,i);break;case"select":var y=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!i.multiple;var v=i.value;v!=null?xn(l,!!i.multiple,v,!1):y!==!!i.multiple&&(i.defaultValue!=null?xn(l,!!i.multiple,i.defaultValue,!0):xn(l,!!i.multiple,i.multiple?[]:"",!1))}l[dr]=i}catch(h){Z(e,e.return,h)}}break;case 6:if(Oe(t,e),Ke(e),r&4){if(e.stateNode===null)throw Error(b(162));l=e.stateNode,i=e.memoizedProps;try{l.nodeValue=i}catch(h){Z(e,e.return,h)}}break;case 3:if(Oe(t,e),Ke(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ir(t.containerInfo)}catch(h){Z(e,e.return,h)}break;case 4:Oe(t,e),Ke(e);break;case 13:Oe(t,e),Ke(e),l=e.child,l.flags&8192&&(i=l.memoizedState!==null,l.stateNode.isHidden=i,!i||l.alternate!==null&&l.alternate.memoizedState!==null||(io=J())),r&4&&xa(e);break;case 22:if(x=n!==null&&n.memoizedState!==null,e.mode&1?(me=(f=me)||x,Oe(t,e),me=f):Oe(t,e),Ke(e),r&8192){if(f=e.memoizedState!==null,(e.stateNode.isHidden=f)&&!x&&e.mode&1)for(L=e,x=e.child;x!==null;){for(g=L=x;L!==null;){switch(y=L,v=y.child,y.tag){case 0:case 11:case 14:case 15:Zn(4,y,y.return);break;case 1:hn(y,y.return);var w=y.stateNode;if(typeof w.componentWillUnmount=="function"){r=y,n=y.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(h){Z(r,n,h)}}break;case 5:hn(y,y.return);break;case 22:if(y.memoizedState!==null){va(g);continue}}v!==null?(v.return=y,L=v):va(g)}x=x.sibling}e:for(x=null,g=e;;){if(g.tag===5){if(x===null){x=g;try{l=g.stateNode,f?(i=l.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=g.stateNode,c=g.memoizedProps.style,o=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=lc("display",o))}catch(h){Z(e,e.return,h)}}}else if(g.tag===6){if(x===null)try{g.stateNode.nodeValue=f?"":g.memoizedProps}catch(h){Z(e,e.return,h)}}else if((g.tag!==22&&g.tag!==23||g.memoizedState===null||g===e)&&g.child!==null){g.child.return=g,g=g.child;continue}if(g===e)break e;for(;g.sibling===null;){if(g.return===null||g.return===e)break e;x===g&&(x=null),g=g.return}x===g&&(x=null),g.sibling.return=g.return,g=g.sibling}}break;case 19:Oe(t,e),Ke(e),r&4&&xa(e);break;case 21:break;default:Oe(t,e),Ke(e)}}function Ke(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Mu(n)){var r=n;break e}n=n.return}throw Error(b(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(nr(l,""),r.flags&=-33);var i=ga(e);ui(e,i,l);break;case 3:case 4:var o=r.stateNode.containerInfo,a=ga(e);ci(e,a,o);break;default:throw Error(b(161))}}catch(c){Z(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function gf(e,t,n){L=e,_u(e)}function _u(e,t,n){for(var r=(e.mode&1)!==0;L!==null;){var l=L,i=l.child;if(l.tag===22&&r){var o=l.memoizedState!==null||Ur;if(!o){var a=l.alternate,c=a!==null&&a.memoizedState!==null||me;a=Ur;var f=me;if(Ur=o,(me=c)&&!f)for(L=l;L!==null;)o=L,c=o.child,o.tag===22&&o.memoizedState!==null?ja(l):c!==null?(c.return=o,L=c):ja(l);for(;i!==null;)L=i,_u(i),i=i.sibling;L=l,Ur=a,me=f}ya(e)}else l.subtreeFlags&8772&&i!==null?(i.return=l,L=i):ya(e)}}function ya(e){for(;L!==null;){var t=L;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:me||Fs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!me)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Ue(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&na(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}na(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var f=t.alternate;if(f!==null){var x=f.memoizedState;if(x!==null){var g=x.dehydrated;g!==null&&ir(g)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(b(163))}me||t.flags&512&&ai(t)}catch(y){Z(t,t.return,y)}}if(t===e){L=null;break}if(n=t.sibling,n!==null){n.return=t.return,L=n;break}L=t.return}}function va(e){for(;L!==null;){var t=L;if(t===e){L=null;break}var n=t.sibling;if(n!==null){n.return=t.return,L=n;break}L=t.return}}function ja(e){for(;L!==null;){var t=L;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Fs(4,t)}catch(c){Z(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(c){Z(t,l,c)}}var i=t.return;try{ai(t)}catch(c){Z(t,i,c)}break;case 5:var o=t.return;try{ai(t)}catch(c){Z(t,o,c)}}}catch(c){Z(t,t.return,c)}if(t===e){L=null;break}var a=t.sibling;if(a!==null){a.return=t.return,L=a;break}L=t.return}}var xf=Math.ceil,Ns=ct.ReactCurrentDispatcher,so=ct.ReactCurrentOwner,Pe=ct.ReactCurrentBatchConfig,$=0,le=null,ee=null,ae=0,Ce=0,gn=Dt(0),ne=0,xr=null,qt=0,Vs=0,lo=0,Jn=null,je=null,io=0,Tn=1/0,Ze=null,Ss=!1,di=null,Nt=null,Fr=!1,gt=null,ks=0,er=0,mi=null,Jr=-1,es=0;function ge(){return $&6?J():Jr!==-1?Jr:Jr=J()}function St(e){return e.mode&1?$&2&&ae!==0?ae&-ae:Jm.transition!==null?(es===0&&(es=yc()),es):(e=F,e!==0||(e=window.event,e=e===void 0?16:bc(e.type)),e):1}function We(e,t,n,r){if(50<er)throw er=0,mi=null,Error(b(185));jr(e,n,r),(!($&2)||e!==le)&&(e===le&&(!($&2)&&(Vs|=n),ne===4&&pt(e,ae)),ke(e,r),n===1&&$===0&&!(t.mode&1)&&(Tn=J()+500,$s&&Mt()))}function ke(e,t){var n=e.callbackNode;Jd(e,t);var r=os(e,e===le?ae:0);if(r===0)n!==null&&To(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&To(n),t===1)e.tag===0?Zm(wa.bind(null,e)):Bc(wa.bind(null,e)),Gm(function(){!($&6)&&Mt()}),n=null;else{switch(vc(r)){case 1:n=Ai;break;case 4:n=gc;break;case 16:n=is;break;case 536870912:n=xc;break;default:n=is}n=Vu(n,zu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function zu(e,t){if(Jr=-1,es=0,$&6)throw Error(b(327));var n=e.callbackNode;if(Nn()&&e.callbackNode!==n)return null;var r=os(e,e===le?ae:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=bs(e,r);else{t=r;var l=$;$|=2;var i=Ru();(le!==e||ae!==t)&&(Ze=null,Tn=J()+500,Bt(e,t));do try{jf();break}catch(a){Pu(e,a)}while(1);Wi(),Ns.current=i,$=l,ee!==null?t=0:(le=null,ae=0,t=ne)}if(t!==0){if(t===2&&(l=Ol(e),l!==0&&(r=l,t=fi(e,l))),t===1)throw n=xr,Bt(e,0),pt(e,r),ke(e,J()),n;if(t===6)pt(e,r);else{if(l=e.current.alternate,!(r&30)&&!yf(l)&&(t=bs(e,r),t===2&&(i=Ol(e),i!==0&&(r=i,t=fi(e,i))),t===1))throw n=xr,Bt(e,0),pt(e,r),ke(e,J()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(b(345));case 2:$t(e,je,Ze);break;case 3:if(pt(e,r),(r&130023424)===r&&(t=io+500-J(),10<t)){if(os(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){ge(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Kl($t.bind(null,e,je,Ze),t);break}$t(e,je,Ze);break;case 4:if(pt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var o=31-Be(r);i=1<<o,o=t[o],o>l&&(l=o),r&=~i}if(r=l,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*xf(r/1960))-r,10<r){e.timeoutHandle=Kl($t.bind(null,e,je,Ze),r);break}$t(e,je,Ze);break;case 5:$t(e,je,Ze);break;default:throw Error(b(329))}}}return ke(e,J()),e.callbackNode===n?zu.bind(null,e):null}function fi(e,t){var n=Jn;return e.current.memoizedState.isDehydrated&&(Bt(e,t).flags|=256),e=bs(e,t),e!==2&&(t=je,je=n,t!==null&&pi(t)),e}function pi(e){je===null?je=e:je.push.apply(je,e)}function yf(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],i=l.getSnapshot;l=l.value;try{if(!Qe(i(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function pt(e,t){for(t&=~lo,t&=~Vs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Be(t),r=1<<n;e[n]=-1,t&=~r}}function wa(e){if($&6)throw Error(b(327));Nn();var t=os(e,0);if(!(t&1))return ke(e,J()),null;var n=bs(e,t);if(e.tag!==0&&n===2){var r=Ol(e);r!==0&&(t=r,n=fi(e,r))}if(n===1)throw n=xr,Bt(e,0),pt(e,t),ke(e,J()),n;if(n===6)throw Error(b(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,$t(e,je,Ze),ke(e,J()),null}function oo(e,t){var n=$;$|=1;try{return e(t)}finally{$=n,$===0&&(Tn=J()+500,$s&&Mt())}}function Xt(e){gt!==null&&gt.tag===0&&!($&6)&&Nn();var t=$;$|=1;var n=Pe.transition,r=F;try{if(Pe.transition=null,F=1,e)return e()}finally{F=r,Pe.transition=n,$=t,!($&6)&&Mt()}}function ao(){Ce=gn.current,Q(gn)}function Bt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Km(n)),ee!==null)for(n=ee.return;n!==null;){var r=n;switch(Vi(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ms();break;case 3:En(),Q(Ne),Q(fe),Yi();break;case 5:Xi(r);break;case 4:En();break;case 13:Q(q);break;case 19:Q(q);break;case 10:Qi(r.type._context);break;case 22:case 23:ao()}n=n.return}if(le=e,ee=e=kt(e.current,null),ae=Ce=t,ne=0,xr=null,lo=Vs=qt=0,je=Jn=null,Vt!==null){for(t=0;t<Vt.length;t++)if(n=Vt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=l,r.next=o}n.pending=r}Vt=null}return e}function Pu(e,t){do{var n=ee;try{if(Wi(),Xr.current=ws,js){for(var r=X.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}js=!1}if(Gt=0,se=te=X=null,Yn=!1,pr=0,so.current=null,n===null||n.return===null){ne=1,xr=t,ee=null;break}e:{var i=e,o=n.return,a=n,c=t;if(t=ae,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var f=c,x=a,g=x.tag;if(!(x.mode&1)&&(g===0||g===11||g===15)){var y=x.alternate;y?(x.updateQueue=y.updateQueue,x.memoizedState=y.memoizedState,x.lanes=y.lanes):(x.updateQueue=null,x.memoizedState=null)}var v=aa(o);if(v!==null){v.flags&=-257,ca(v,o,a,i,t),v.mode&1&&oa(i,f,t),t=v,c=f;var w=t.updateQueue;if(w===null){var h=new Set;h.add(c),t.updateQueue=h}else w.add(c);break e}else{if(!(t&1)){oa(i,f,t),co();break e}c=Error(b(426))}}else if(G&&a.mode&1){var N=aa(o);if(N!==null){!(N.flags&65536)&&(N.flags|=256),ca(N,o,a,i,t),Hi(Ln(c,a));break e}}i=c=Ln(c,a),ne!==4&&(ne=2),Jn===null?Jn=[i]:Jn.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=vu(i,c,t);ta(i,m);break e;case 1:a=c;var d=i.type,u=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Nt===null||!Nt.has(u)))){i.flags|=65536,t&=-t,i.lanes|=t;var p=ju(i,a,t);ta(i,p);break e}}i=i.return}while(i!==null)}Ou(n)}catch(j){t=j,ee===n&&n!==null&&(ee=n=n.return);continue}break}while(1)}function Ru(){var e=Ns.current;return Ns.current=ws,e===null?ws:e}function co(){(ne===0||ne===3||ne===2)&&(ne=4),le===null||!(qt&268435455)&&!(Vs&268435455)||pt(le,ae)}function bs(e,t){var n=$;$|=2;var r=Ru();(le!==e||ae!==t)&&(Ze=null,Bt(e,t));do try{vf();break}catch(l){Pu(e,l)}while(1);if(Wi(),$=n,Ns.current=r,ee!==null)throw Error(b(261));return le=null,ae=0,ne}function vf(){for(;ee!==null;)$u(ee)}function jf(){for(;ee!==null&&!Bd();)$u(ee)}function $u(e){var t=Fu(e.alternate,e,Ce);e.memoizedProps=e.pendingProps,t===null?Ou(e):ee=t,so.current=null}function Ou(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=ff(n,t),n!==null){n.flags&=32767,ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,ee=null;return}}else if(n=mf(n,t,Ce),n!==null){ee=n;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);ne===0&&(ne=5)}function $t(e,t,n){var r=F,l=Pe.transition;try{Pe.transition=null,F=1,wf(e,t,n,r)}finally{Pe.transition=l,F=r}return null}function wf(e,t,n,r){do Nn();while(gt!==null);if($&6)throw Error(b(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(b(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(em(e,i),e===le&&(ee=le=null,ae=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Fr||(Fr=!0,Vu(is,function(){return Nn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Pe.transition,Pe.transition=null;var o=F;F=1;var a=$;$|=4,so.current=null,hf(e,n),Iu(n,e),Um(Wl),as=!!Bl,Wl=Bl=null,e.current=n,gf(n),Wd(),$=a,F=o,Pe.transition=i}else e.current=n;if(Fr&&(Fr=!1,gt=e,ks=l),i=e.pendingLanes,i===0&&(Nt=null),Gd(n.stateNode),ke(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(Ss)throw Ss=!1,e=di,di=null,e;return ks&1&&e.tag!==0&&Nn(),i=e.pendingLanes,i&1?e===mi?er++:(er=0,mi=e):er=0,Mt(),null}function Nn(){if(gt!==null){var e=vc(ks),t=Pe.transition,n=F;try{if(Pe.transition=null,F=16>e?16:e,gt===null)var r=!1;else{if(e=gt,gt=null,ks=0,$&6)throw Error(b(331));var l=$;for($|=4,L=e.current;L!==null;){var i=L,o=i.child;if(L.flags&16){var a=i.deletions;if(a!==null){for(var c=0;c<a.length;c++){var f=a[c];for(L=f;L!==null;){var x=L;switch(x.tag){case 0:case 11:case 15:Zn(8,x,i)}var g=x.child;if(g!==null)g.return=x,L=g;else for(;L!==null;){x=L;var y=x.sibling,v=x.return;if(Du(x),x===f){L=null;break}if(y!==null){y.return=v,L=y;break}L=v}}}var w=i.alternate;if(w!==null){var h=w.child;if(h!==null){w.child=null;do{var N=h.sibling;h.sibling=null,h=N}while(h!==null)}}L=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,L=o;else e:for(;L!==null;){if(i=L,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Zn(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,L=m;break e}L=i.return}}var d=e.current;for(L=d;L!==null;){o=L;var u=o.child;if(o.subtreeFlags&2064&&u!==null)u.return=o,L=u;else e:for(o=d;L!==null;){if(a=L,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Fs(9,a)}}catch(j){Z(a,a.return,j)}if(a===o){L=null;break e}var p=a.sibling;if(p!==null){p.return=a.return,L=p;break e}L=a.return}}if($=l,Mt(),Xe&&typeof Xe.onPostCommitFiberRoot=="function")try{Xe.onPostCommitFiberRoot(Is,e)}catch{}r=!0}return r}finally{F=n,Pe.transition=t}}return!1}function Na(e,t,n){t=Ln(n,t),t=vu(e,t,1),e=wt(e,t,1),t=ge(),e!==null&&(jr(e,1,t),ke(e,t))}function Z(e,t,n){if(e.tag===3)Na(e,e,n);else for(;t!==null;){if(t.tag===3){Na(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Nt===null||!Nt.has(r))){e=Ln(n,e),e=ju(t,e,1),t=wt(t,e,1),e=ge(),t!==null&&(jr(t,1,e),ke(t,e));break}}t=t.return}}function Nf(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ge(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(ae&n)===n&&(ne===4||ne===3&&(ae&130023424)===ae&&500>J()-io?Bt(e,0):lo|=n),ke(e,t)}function Uu(e,t){t===0&&(e.mode&1?(t=Mr,Mr<<=1,!(Mr&130023424)&&(Mr=4194304)):t=1);var n=ge();e=ot(e,t),e!==null&&(jr(e,t,n),ke(e,n))}function Sf(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Uu(e,n)}function kf(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(b(314))}r!==null&&r.delete(t),Uu(e,n)}var Fu;Fu=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ne.current)we=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return we=!1,df(e,t,n);we=!!(e.flags&131072)}else we=!1,G&&t.flags&1048576&&Wc(t,hs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Zr(e,t),e=t.pendingProps;var l=kn(t,fe.current);wn(t,n),l=Ji(null,t,r,e,l,n);var i=eo();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Se(r)?(i=!0,fs(t)):i=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Gi(t),l.updater=Us,t.stateNode=l,l._reactInternals=t,ei(t,r,e,n),t=ri(null,t,r,!0,i,n)):(t.tag=0,G&&i&&Fi(t),he(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Zr(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=Cf(r),e=Ue(r,e),l){case 0:t=ni(null,t,r,e,n);break e;case 1:t=ma(null,t,r,e,n);break e;case 11:t=ua(null,t,r,e,n);break e;case 14:t=da(null,t,r,Ue(r.type,e),n);break e}throw Error(b(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Ue(r,l),ni(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Ue(r,l),ma(e,t,r,l,n);case 3:e:{if(ku(t),e===null)throw Error(b(387));r=t.pendingProps,i=t.memoizedState,l=i.element,Yc(e,t),ys(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){l=Ln(Error(b(423)),t),t=fa(e,t,r,n,l);break e}else if(r!==l){l=Ln(Error(b(424)),t),t=fa(e,t,r,n,l);break e}else for(Ee=jt(t.stateNode.containerInfo.firstChild),Le=t,G=!0,Ve=null,n=qc(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(bn(),r===l){t=at(e,t,n);break e}he(e,t,r,n)}t=t.child}return t;case 5:return Zc(t),e===null&&Yl(t),r=t.type,l=t.pendingProps,i=e!==null?e.memoizedProps:null,o=l.children,Ql(r,l)?o=null:i!==null&&Ql(r,i)&&(t.flags|=32),Su(e,t),he(e,t,o,n),t.child;case 6:return e===null&&Yl(t),null;case 13:return bu(e,t,n);case 4:return qi(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Cn(t,null,r,n):he(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Ue(r,l),ua(e,t,r,l,n);case 7:return he(e,t,t.pendingProps,n),t.child;case 8:return he(e,t,t.pendingProps.children,n),t.child;case 12:return he(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,i=t.memoizedProps,o=l.value,B(gs,r._currentValue),r._currentValue=o,i!==null)if(Qe(i.value,o)){if(i.children===l.children&&!Ne.current){t=at(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var c=a.firstContext;c!==null;){if(c.context===r){if(i.tag===1){c=st(-1,n&-n),c.tag=2;var f=i.updateQueue;if(f!==null){f=f.shared;var x=f.pending;x===null?c.next=c:(c.next=x.next,x.next=c),f.pending=c}}i.lanes|=n,c=i.alternate,c!==null&&(c.lanes|=n),Zl(i.return,n,t),a.lanes|=n;break}c=c.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(b(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Zl(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}he(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,wn(t,n),l=Re(l),r=r(l),t.flags|=1,he(e,t,r,n),t.child;case 14:return r=t.type,l=Ue(r,t.pendingProps),l=Ue(r.type,l),da(e,t,r,l,n);case 15:return wu(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Ue(r,l),Zr(e,t),t.tag=1,Se(r)?(e=!0,fs(t)):e=!1,wn(t,n),yu(t,r,l),ei(t,r,l,n),ri(null,t,r,!0,e,n);case 19:return Cu(e,t,n);case 22:return Nu(e,t,n)}throw Error(b(156,t.tag))};function Vu(e,t){return hc(e,t)}function bf(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new bf(e,t,n,r)}function uo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Cf(e){if(typeof e=="function")return uo(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ti)return 11;if(e===Di)return 14}return 2}function kt(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ts(e,t,n,r,l,i){var o=2;if(r=e,typeof e=="function")uo(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case ln:return Wt(n.children,l,i,t);case Li:o=8,l|=8;break;case kl:return e=ze(12,n,t,l|2),e.elementType=kl,e.lanes=i,e;case bl:return e=ze(13,n,t,l),e.elementType=bl,e.lanes=i,e;case Cl:return e=ze(19,n,t,l),e.elementType=Cl,e.lanes=i,e;case Za:return Hs(n,l,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Xa:o=10;break e;case Ya:o=9;break e;case Ti:o=11;break e;case Di:o=14;break e;case dt:o=16,r=null;break e}throw Error(b(130,e==null?e:typeof e,""))}return t=ze(o,n,t,l),t.elementType=e,t.type=r,t.lanes=i,t}function Wt(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function Hs(e,t,n,r){return e=ze(22,e,r,t),e.elementType=Za,e.lanes=n,e.stateNode={isHidden:!1},e}function yl(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function vl(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ef(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Js(0),this.expirationTimes=Js(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Js(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function mo(e,t,n,r,l,i,o,a,c){return e=new Ef(e,t,n,a,c),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ze(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Gi(i),e}function Lf(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:sn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Hu(e){if(!e)return Lt;e=e._reactInternals;e:{if(Zt(e)!==e||e.tag!==1)throw Error(b(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Se(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(b(171))}if(e.tag===1){var n=e.type;if(Se(n))return Hc(e,n,t)}return t}function Bu(e,t,n,r,l,i,o,a,c){return e=mo(n,r,!0,e,l,i,o,a,c),e.context=Hu(null),n=e.current,r=ge(),l=St(n),i=st(r,l),i.callback=t??null,wt(n,i,l),e.current.lanes=l,jr(e,l,r),ke(e,r),e}function Bs(e,t,n,r){var l=t.current,i=ge(),o=St(l);return n=Hu(n),t.context===null?t.context=n:t.pendingContext=n,t=st(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=wt(l,t,o),e!==null&&(We(e,l,o,i),qr(e,l,o)),o}function Cs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Sa(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function fo(e,t){Sa(e,t),(e=e.alternate)&&Sa(e,t)}function Tf(){return null}var Wu=typeof reportError=="function"?reportError:function(e){console.error(e)};function po(e){this._internalRoot=e}Ws.prototype.render=po.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(b(409));Bs(e,t,null,null)};Ws.prototype.unmount=po.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Xt(function(){Bs(null,e,null,null)}),t[it]=null}};function Ws(e){this._internalRoot=e}Ws.prototype.unstable_scheduleHydration=function(e){if(e){var t=Nc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ft.length&&t!==0&&t<ft[n].priority;n++);ft.splice(n,0,e),n===0&&kc(e)}};function ho(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Qs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ka(){}function Df(e,t,n,r,l){if(l){if(typeof r=="function"){var i=r;r=function(){var f=Cs(o);i.call(f)}}var o=Bu(t,r,e,0,null,!1,!1,"",ka);return e._reactRootContainer=o,e[it]=o.current,cr(e.nodeType===8?e.parentNode:e),Xt(),o}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var a=r;r=function(){var f=Cs(c);a.call(f)}}var c=mo(e,0,!1,null,null,!1,!1,"",ka);return e._reactRootContainer=c,e[it]=c.current,cr(e.nodeType===8?e.parentNode:e),Xt(function(){Bs(t,c,n,r)}),c}function Ks(e,t,n,r,l){var i=n._reactRootContainer;if(i){var o=i;if(typeof l=="function"){var a=l;l=function(){var c=Cs(o);a.call(c)}}Bs(t,o,e,l)}else o=Df(n,t,e,l,r);return Cs(o)}jc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Bn(t.pendingLanes);n!==0&&(Ii(t,n|1),ke(t,J()),!($&6)&&(Tn=J()+500,Mt()))}break;case 13:Xt(function(){var r=ot(e,1);if(r!==null){var l=ge();We(r,e,1,l)}}),fo(e,1)}};_i=function(e){if(e.tag===13){var t=ot(e,134217728);if(t!==null){var n=ge();We(t,e,134217728,n)}fo(e,134217728)}};wc=function(e){if(e.tag===13){var t=St(e),n=ot(e,t);if(n!==null){var r=ge();We(n,e,t,r)}fo(e,t)}};Nc=function(){return F};Sc=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};Pl=function(e,t,n){switch(t){case"input":if(Tl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Rs(r);if(!l)throw Error(b(90));ec(r),Tl(r,l)}}}break;case"textarea":nc(e,n);break;case"select":t=n.value,t!=null&&xn(e,!!n.multiple,t,!1)}};cc=oo;uc=Xt;var Mf={usingClientEntryPoint:!1,Events:[Nr,un,Rs,oc,ac,oo]},Fn={findFiberByHostInstance:Ft,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Af={bundleType:Fn.bundleType,version:Fn.version,rendererPackageName:Fn.rendererPackageName,rendererConfig:Fn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ct.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=fc(e),e===null?null:e.stateNode},findFiberByHostInstance:Fn.findFiberByHostInstance||Tf,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Vr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Vr.isDisabled&&Vr.supportsFiber)try{Is=Vr.inject(Af),Xe=Vr}catch{}}De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Mf;De.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ho(t))throw Error(b(200));return Lf(e,t,null,n)};De.createRoot=function(e,t){if(!ho(e))throw Error(b(299));var n=!1,r="",l=Wu;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=mo(e,1,!1,null,null,n,!1,r,l),e[it]=t.current,cr(e.nodeType===8?e.parentNode:e),new po(t)};De.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(b(188)):(e=Object.keys(e).join(","),Error(b(268,e)));return e=fc(t),e=e===null?null:e.stateNode,e};De.flushSync=function(e){return Xt(e)};De.hydrate=function(e,t,n){if(!Qs(t))throw Error(b(200));return Ks(null,e,t,!0,n)};De.hydrateRoot=function(e,t,n){if(!ho(e))throw Error(b(405));var r=n!=null&&n.hydratedSources||null,l=!1,i="",o=Wu;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Bu(t,null,e,1,n??null,l,!1,i,o),e[it]=t.current,cr(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Ws(t)};De.render=function(e,t,n){if(!Qs(t))throw Error(b(200));return Ks(null,e,t,!1,n)};De.unmountComponentAtNode=function(e){if(!Qs(e))throw Error(b(40));return e._reactRootContainer?(Xt(function(){Ks(null,null,e,!1,function(){e._reactRootContainer=null,e[it]=null})}),!0):!1};De.unstable_batchedUpdates=oo;De.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Qs(n))throw Error(b(200));if(e==null||e._reactInternals===void 0)throw Error(b(38));return Ks(e,t,n,!1,r)};De.version="18.3.1-next-f1338f8080-20240426";function Qu(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Qu)}catch(e){console.error(e)}}Qu(),Qa.exports=De;var If=Qa.exports,ba=If;Nl.createRoot=ba.createRoot,Nl.hydrateRoot=ba.hydrateRoot;const jl="http://localhost:5000/api";class _f{constructor(t){Ae(this,"baseURL");this.baseURL=t}async request(t,n={}){const r=`${this.baseURL}${t}`,l=new AbortController,i=setTimeout(()=>l.abort(),1e4),o={headers:{"Content-Type":"application/json",...n.headers},signal:l.signal,...n};try{const a=await fetch(r,o);if(clearTimeout(i),!a.ok){const c=await a.json().catch(()=>({}));throw new Error(c.error||`HTTP ${a.status}: ${a.statusText}`)}return await a.json()}catch(a){throw clearTimeout(i),console.error(`API request failed: ${r}`,a),a}}async get(t){return this.request(t,{method:"GET"})}async post(t,n){return this.request(t,{method:"POST",body:n?JSON.stringify(n):void 0})}async put(t,n){return this.request(t,{method:"PUT",body:n?JSON.stringify(n):void 0})}async delete(t){return this.request(t,{method:"DELETE"})}}class zf{constructor(){Ae(this,"client");Ae(this,"isOnline",!0);Ae(this,"connectionPromise",null);this.client=new _f(jl)}async checkConnection(){if(this.connectionPromise)return console.log("[ApiService] Connection check already in progress, reusing promise"),this.connectionPromise;this.connectionPromise=this._performConnectionCheck();try{return await this.connectionPromise}finally{this.connectionPromise=null}}async _performConnectionCheck(){try{const n=`${jl.replace("/api","")}/health`;console.log(`[ApiService] Checking connection to: ${n}`);const r=new AbortController;let l=null;try{l=setTimeout(()=>{console.log("[ApiService] Connection check timeout, aborting request"),r.abort()},8e3);const i=await fetch(n,{signal:r.signal,method:"GET",cache:"no-cache",headers:{"Cache-Control":"no-cache"}});return l&&(clearTimeout(l),l=null),console.log(`[ApiService] Response status: ${i.status}`),i.ok?(this.isOnline=!0,console.log("[ApiService] Connection check successful"),!0):(this.isOnline=!1,console.warn(`[ApiService] Connection check failed with status: ${i.status}`),!1)}catch(i){throw l&&clearTimeout(l),i}}catch(t){return console.warn("[ApiService] Backend server is not available:",t),t instanceof Error&&(t.name==="AbortError"?console.warn("[ApiService] Request was aborted (likely due to timeout)"):t.name==="TypeError"&&t.message.includes("fetch")?console.warn("[ApiService] Network error or server not reachable"):console.warn(`[ApiService] Unexpected error: ${t.name} - ${t.message}`)),this.isOnline=!1,!1}}getConnectionStatus(){return this.isOnline}async getLLMConfigs(){return this.client.get("/llm-configs")}async createLLMConfig(t){return this.client.post("/llm-configs",t)}async updateLLMConfig(t,n){return this.client.put(`/llm-configs/${t}`,n)}async deleteLLMConfig(t){try{await this.client.delete(`/llm-configs/${t}`)}catch(n){throw n instanceof Error&&n.message.includes("无法删除正在使用的LLM配置")?new Error("无法删除正在使用的LLM配置，请先从智能体中移除此配置"):n}}async getAgents(){return this.client.get("/agents")}async createAgent(t){return this.client.post("/agents",t)}async updateAgent(t,n){return this.client.put(`/agents/${t}`,n)}async deleteAgent(t){await this.client.delete(`/agents/${t}`)}async getDiscussions(){return this.client.get("/discussions")}async createDiscussion(t){return this.client.post("/discussions",t)}async updateDiscussion(t,n){return this.client.put(`/discussions/${t}`,n)}async deleteDiscussion(t){await this.client.delete(`/discussions/${t}`)}async addMessage(t,n){return this.client.post(`/discussions/${t}/messages`,n)}async getSettings(){return this.client.get("/settings")}async updateSettings(t){return this.client.put("/settings",t)}async getPreferences(){return this.client.get("/preferences")}async updatePreferences(t){return this.client.put("/preferences",t)}async exportData(){return this.client.get("/data/export")}async importData(t,n=!1){await this.client.post("/data/import",{...t,clearExisting:n})}async clearAllData(){await this.client.delete("/data/clear")}async getStorageInfo(){return this.client.get("/storage/info")}}const V=new zf,Ot=class Ot{constructor(){Ae(this,"isInitialized",!1);Ae(this,"storageMode","server");Ae(this,"serverAvailable",!1)}static getInstance(){return Ot.instance||(Ot.instance=new Ot),Ot.instance}async initialize(){if(this.isInitialized)return;console.log("Starting StorageService initialization..."),console.log("Checking server connection..."),console.log("API_BASE_URL: http://localhost:5000/api");let t=0;const n=3;for(;t<n&&!this.serverAvailable;){t++,console.log(`[StorageService] Connection attempt ${t}/${n}`);try{if(this.serverAvailable=await V.checkConnection(),console.log(`[StorageService] Connection check result: ${this.serverAvailable}`),this.serverAvailable)break;t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(r=>setTimeout(r,2e3)))}catch(r){console.error(`Server connection check failed (attempt ${t}):`,r),this.serverAvailable=!1,t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(l=>setTimeout(l,2e3)))}}if(console.log(`Server available: ${this.serverAvailable}`),!this.serverAvailable)throw new Error("服务器连接失败，请确保后端服务正在运行");await this.determineStorageMode(),console.log(`Storage mode: ${this.storageMode}`),await this.initializeDefaultSettings(),console.log("Default settings initialized"),await this.migrateData(),console.log("Data migration completed"),this.isInitialized=!0,console.log(`StorageService initialized successfully with ${this.storageMode} mode`)}async determineStorageMode(){this.storageMode="server"}setStorageMode(t){this.storageMode=t}getStorageMode(){return this.storageMode}isServerAvailable(){return this.serverAvailable}async refreshServerConnection(){return this.serverAvailable=await V.checkConnection(),this.serverAvailable}async initializeDefaultSettings(){const t={version:"1.0.0",lastUpdated:new Date().toISOString(),autoSave:!0,maxStoredDiscussions:100,defaultDiscussionMode:"free",theme:"light"},n={defaultAgentCount:3,preferredLLMProvider:"openai",autoStartDiscussion:!1,showAdvancedOptions:!1,notificationsEnabled:!0,exportFormat:"json"};this.getSettings()||this.saveSettings(t),this.getPreferences()||this.savePreferences(n)}async migrateData(){const t=await this.getSettings();((t==null?void 0:t.version)||"0.0.0")<"1.0.0"&&console.log("Migrating data to version 1.0.0...")}async saveAgents(t){try{if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${t.length} agents`);const n=await this.getAgents(),r=t.map(async l=>n.findIndex(o=>o.id===l.id)>=0?V.updateAgent(l.id,l):V.createAgent(l));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} agents to server`)}else throw new Error("服务器不可用，无法保存智能体数据");this.updateLastModified()}catch(n){throw console.error("Failed to save agents:",n),new Error("Failed to save agents to storage")}}async getAgents(){if(this.serverAvailable)return await V.getAgents();throw new Error("服务器不可用，无法获取智能体数据")}async saveAgent(t){if(this.serverAvailable)(await this.getAgents()).findIndex(l=>l.id===t.id)>=0?await V.updateAgent(t.id,t):await V.createAgent(t);else throw new Error("服务器不可用，无法保存智能体")}async deleteAgent(t){if(this.serverAvailable)await V.deleteAgent(t);else throw new Error("服务器不可用，无法删除智能体")}async saveLLMConfigs(t){console.log(`[StorageService] Starting saveLLMConfigs with ${t.length} configs`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`);try{if(this.serverAvailable){console.log("[StorageService] Attempting to save LLM configs to server...");const n=await this.getLLMConfigs(),r=t.map(async l=>n.findIndex(o=>o.id===l.id)>=0?V.updateLLMConfig(l.id,l):V.createLLMConfig(l));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} LLM configs to server`)}else throw new Error("服务器不可用，无法保存LLM配置");this.updateLastModified(),console.log("[StorageService] saveLLMConfigs completed successfully")}catch(n){throw console.error("[StorageService] Failed to save LLM configs:",n),new Error("Failed to save LLM configs to storage")}}async getLLMConfigs(){if(this.serverAvailable){console.log("[StorageService] Using SERVER mode - attempting to load from server");const t=await V.getLLMConfigs();return console.log(`[StorageService] Successfully loaded ${t.length} configs from server`),t}else throw new Error("服务器不可用，无法获取LLM配置数据")}async saveLLMConfig(t){if(console.log(`[StorageService] Starting saveLLMConfig for config: ${t.id} (${t.name})`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable){console.log("[StorageService] Attempting to save single config to server...");const r=(await this.getLLMConfigs()).findIndex(l=>l.id===t.id);console.log(`[StorageService] Existing config index: ${r}`),r>=0?(await V.updateLLMConfig(t.id,t),console.log("[StorageService] Config updated on server successfully")):(await V.createLLMConfig(t),console.log("[StorageService] Config created on server successfully")),console.log("[StorageService] saveLLMConfig completed successfully")}else throw new Error("服务器不可用，无法保存LLM配置")}async deleteLLMConfig(t){if(console.log(`[StorageService] Starting deleteLLMConfig for config: ${t}`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable)console.log("[StorageService] Attempting to delete config from server..."),await V.deleteLLMConfig(t),console.log("[StorageService] Config deleted from server successfully"),console.log("[StorageService] deleteLLMConfig completed successfully");else throw new Error("服务器不可用，无法删除LLM配置")}async exportLLMConfigs(){try{const n=(await this.getLLMConfigs()).map(r=>({...r,apiKey:"***HIDDEN***"}));return JSON.stringify(n,null,2)}catch(t){throw console.error("Failed to export LLM configs:",t),new Error("导出LLM配置失败")}}async importLLMConfigs(t){const n={success:0,errors:[]};try{const r=JSON.parse(t);if(!Array.isArray(r))throw new Error("数据格式错误：应该是配置数组");for(const l of r)try{if(!l.id||!l.name||!l.provider||!l.model){n.errors.push(`配置 ${l.name||"Unknown"} 缺少必要字段`);continue}if(l.apiKey==="***HIDDEN***"){n.errors.push(`配置 ${l.name} 的API密钥需要重新设置`);continue}await this.saveLLMConfig(l),n.success++}catch(i){n.errors.push(`导入配置 ${l.name||"Unknown"} 失败: ${i instanceof Error?i.message:"未知错误"}`)}}catch(r){n.errors.push("解析JSON数据失败: "+(r instanceof Error?r.message:"未知错误"))}return n}generateLLMConfigId(){return`llm_${Date.now()}_${Math.random().toString(36).substring(2,11)}`}createDefaultLLMConfig(t,n,r){return{id:this.generateLLMConfigId(),name:t.name,provider:t.provider.toLowerCase(),model:t.model,apiKey:n,baseURL:r,temperature:t.defaultSettings.temperature,maxTokens:t.defaultSettings.maxTokens}}async getLLMConfig(t){try{return(await this.getLLMConfigs()).find(r=>r.id===t)||null}catch(n){return console.error("Failed to get LLM config:",n),null}}validateLLMConfig(t){var r,l,i,o;const n=[];return(r=t.name)!=null&&r.trim()||n.push("配置名称不能为空"),t.provider||n.push("请选择提供商"),(l=t.model)!=null&&l.trim()||n.push("模型名称不能为空"),(i=t.apiKey)!=null&&i.trim()||n.push("API密钥不能为空"),t.temperature!==void 0&&(t.temperature<0||t.temperature>2)&&n.push("温度值应在0-2之间"),t.maxTokens!==void 0&&(t.maxTokens<1||t.maxTokens>4e3)&&n.push("最大令牌数应在1-4000之间"),t.provider==="azure"&&!((o=t.baseURL)!=null&&o.trim())&&n.push("Azure提供商需要设置基础URL"),n}async getLLMConfigStats(){try{const t=await this.getLLMConfigs(),n={};t.forEach(l=>{n[l.provider]=(n[l.provider]||0)+1});const r=t.slice(0,5);return{total:t.length,byProvider:n,recentlyUsed:r}}catch(t){return console.error("Failed to get LLM config stats:",t),{total:0,byProvider:{},recentlyUsed:[]}}}async saveDiscussions(t){try{const n=await this.getSettings(),r=(n==null?void 0:n.maxStoredDiscussions)||100,l=t.sort((i,o)=>new Date(o.createdAt).getTime()-new Date(i.createdAt).getTime()).slice(0,r);if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${l.length} discussions`);const i=await this.getDiscussions(),o=l.map(async a=>i.findIndex(f=>f.id===a.id)>=0?V.updateDiscussion(a.id,a):V.createDiscussion(a));await Promise.all(o),console.log(`[StorageService] Successfully saved ${l.length} discussions to server`)}else throw new Error("服务器不可用，无法保存讨论数据");this.updateLastModified()}catch(n){throw console.error("Failed to save discussions:",n),new Error("Failed to save discussions to storage")}}async getDiscussions(){if(this.serverAvailable)return await V.getDiscussions();throw new Error("服务器不可用，无法获取讨论数据")}async saveDiscussion(t){try{if(this.serverAvailable)(await this.getDiscussions()).findIndex(l=>l.id===t.id)>=0?await V.updateDiscussion(t.id,t):await V.createDiscussion(t);else throw new Error("服务器不可用，无法保存讨论")}catch(n){throw console.error("Failed to save discussion:",n),n}}async deleteDiscussion(t){try{if(this.serverAvailable)await V.deleteDiscussion(t);else throw new Error("服务器不可用，无法删除讨论")}catch(n){throw console.error("Failed to delete discussion:",n),n}}async saveSettings(t){if(this.serverAvailable)await V.updateSettings(t);else throw new Error("服务器不可用，无法保存设置")}async getSettings(){if(this.serverAvailable)return await V.getSettings();throw new Error("服务器不可用，无法获取设置数据")}async savePreferences(t){try{if(this.serverAvailable)await V.updatePreferences(t);else throw new Error("服务器不可用，无法保存用户偏好")}catch(n){throw console.error("Failed to save preferences:",n),new Error("Failed to save preferences to storage")}}async getPreferences(){try{if(this.serverAvailable)return await V.getPreferences();throw new Error("服务器不可用，无法获取用户偏好数据")}catch(t){throw console.error("Failed to load preferences:",t),t}}async getAllData(){return{agents:await this.getAgents(),llmConfigs:await this.getLLMConfigs(),discussions:await this.getDiscussions(),settings:await this.getSettings()||{},preferences:await this.getPreferences()||{}}}async importAllData(t){if(this.serverAvailable)await V.importData(t,!1);else throw new Error("服务器不可用，无法导入数据")}async clearAllData(){if(this.serverAvailable)await V.clearAllData();else throw new Error("服务器不可用，无法清除数据")}async updateLastModified(){const t=await this.getSettings();t&&(t.lastUpdated=new Date().toISOString(),await this.saveSettings(t))}validateData(t){try{return!(t.agents&&!Array.isArray(t.agents)||t.llmConfigs&&!Array.isArray(t.llmConfigs)||t.discussions&&!Array.isArray(t.discussions))}catch{return!1}}async getStorageInfo(){if(this.serverAvailable)return await V.getStorageInfo();throw new Error("服务器不可用，无法获取存储信息")}};Ae(Ot,"instance");let hi=Ot;const O=hi.getInstance();let Hr;const Pf=new Uint8Array(16);function Rf(){if(!Hr&&(Hr=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Hr))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Hr(Pf)}const ie=[];for(let e=0;e<256;++e)ie.push((e+256).toString(16).slice(1));function $f(e,t=0){return ie[e[t+0]]+ie[e[t+1]]+ie[e[t+2]]+ie[e[t+3]]+"-"+ie[e[t+4]]+ie[e[t+5]]+"-"+ie[e[t+6]]+ie[e[t+7]]+"-"+ie[e[t+8]]+ie[e[t+9]]+"-"+ie[e[t+10]]+ie[e[t+11]]+ie[e[t+12]]+ie[e[t+13]]+ie[e[t+14]]+ie[e[t+15]]}const Of=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Ca={randomUUID:Of};function gi(e,t,n){if(Ca.randomUUID&&!t&&!e)return Ca.randomUUID();e=e||{};const r=e.random||(e.rng||Rf)();if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,t){n=n||0;for(let l=0;l<16;++l)t[n+l]=r[l];return t}return $f(r)}const Ku={agents:[],currentDiscussion:null,allDiscussions:[],isDiscussionActive:!1,settings:null,preferences:null,isLoading:!0,loadingStep:"storage"},Gu=T.createContext({state:Ku,dispatch:()=>null,addAgent:()=>null,updateAgent:()=>null,deleteAgent:()=>null,startDiscussion:()=>null,setCurrentDiscussion:()=>null,endDiscussion:()=>null,sendMessage:()=>null,updateSettings:()=>null,updatePreferences:()=>null,exportData:async()=>"",importData:async()=>!1,clearAllData:async()=>{}});function Uf(e,t){switch(t.type){case"ADD_AGENT":return{...e,agents:[...e.agents,t.payload]};case"UPDATE_AGENT":return{...e,agents:e.agents.map(o=>o.id===t.payload.id?t.payload:o)};case"DELETE_AGENT":return{...e,agents:e.agents.filter(o=>o.id!==t.payload)};case"START_DISCUSSION":const n={id:gi(),topic:t.payload.topic,mode:t.payload.mode,participants:t.payload.selectedAgents,messages:[],status:"active",consensus:null,createdAt:new Date,consensusScore:0,moderatorId:t.payload.moderatorId,moderatorSummaries:[],topicRelevanceScore:1,moderatorInterventions:0};return{...e,currentDiscussion:n,isDiscussionActive:!0};case"SET_CURRENT_DISCUSSION":return{...e,currentDiscussion:t.payload,isDiscussionActive:!1};case"ADD_MESSAGE":if(!e.currentDiscussion)return e;const r={...e.currentDiscussion,messages:[...e.currentDiscussion.messages,t.payload]};return{...e,currentDiscussion:r};case"UPDATE_CONSENSUS":if(!e.currentDiscussion)return e;const l={...e.currentDiscussion,consensusScore:t.payload.consensusScore,consensus:t.payload.consensus||e.currentDiscussion.consensus,status:t.payload.consensusScore>80?"consensus":e.currentDiscussion.status,moderatorInterventions:t.payload.moderatorInterventions??e.currentDiscussion.moderatorInterventions,topicRelevanceScore:t.payload.topicRelevanceScore??e.currentDiscussion.topicRelevanceScore,moderatorSummaries:t.payload.moderatorSummaries??e.currentDiscussion.moderatorSummaries};return{...e,currentDiscussion:l};case"END_DISCUSSION":if(!e.currentDiscussion)return e;const i={...e.currentDiscussion,status:"ended"};return{...e,currentDiscussion:null,allDiscussions:[i,...e.allDiscussions],isDiscussionActive:!1};case"LOAD_STATE":return t.payload;case"UPDATE_SETTINGS":return{...e,settings:t.payload};case"UPDATE_PREFERENCES":return{...e,preferences:t.payload};case"SET_LOADING":return{...e,isLoading:t.payload};case"SET_LOADING_STEP":return{...e,loadingStep:t.payload};case"SET_ALL_DISCUSSIONS":return{...e,allDiscussions:t.payload};case"INITIALIZE_SUCCESS":return{...e,agents:t.payload.agents,allDiscussions:t.payload.discussions,settings:t.payload.settings,preferences:t.payload.preferences,isLoading:!1,loadingStep:"complete"};default:return e}}function Ff({children:e}){const[t,n]=T.useReducer(Uf,Ku);T.useEffect(()=>{let N=!1;return(async()=>{if(N)return;const d=Date.now();try{console.log("Starting app initialization..."),n({type:"SET_LOADING",payload:!0}),n({type:"SET_LOADING_STEP",payload:"storage"}),console.log("Initializing storage service...");const u=Date.now();if(await O.initialize(),N||(console.log(`Storage service initialized in ${Date.now()-u}ms`),n({type:"SET_LOADING_STEP",payload:"server"}),O.isServerAvailable()||console.warn("后端服务器不可用，系统无法正常工作"),N))return;n({type:"SET_LOADING_STEP",payload:"agents"}),console.log("Loading data...");const j=Date.now(),S=await y();n({type:"SET_LOADING_STEP",payload:"discussions"}),console.log(`Data loaded in ${Date.now()-j}ms`),n({type:"INITIALIZE_SUCCESS",payload:S}),console.log(`App initialization completed in ${Date.now()-d}ms`)}catch(u){throw console.error("Failed to initialize app:",u),console.error("Error details:",{message:u instanceof Error?u.message:"Unknown error",stack:u instanceof Error?u.stack:void 0,initTime:Date.now()-d}),u}})(),()=>{N=!0}},[]),T.useEffect(()=>{if(!t.isLoading){const N=setTimeout(async()=>{try{console.log("Auto-saving data...");const m=[];m.push(O.saveAgents(t.agents));const d=t.currentDiscussion?[...t.allDiscussions,t.currentDiscussion]:t.allDiscussions;m.push(O.saveDiscussions(d)),t.settings&&m.push(O.saveSettings(t.settings)),t.preferences&&m.push(O.savePreferences(t.preferences)),await Promise.all(m),console.log("Auto-save completed")}catch(m){console.error("Failed to auto-save data:",m)}},1e3);return()=>clearTimeout(N)}},[t.agents,t.allDiscussions,t.currentDiscussion,t.settings,t.preferences,t.isLoading]);const r=N=>{const m={...N,id:gi(),isActive:!0};n({type:"ADD_AGENT",payload:m})},l=N=>{n({type:"UPDATE_AGENT",payload:N})},i=async N=>{try{await O.deleteAgent(N),n({type:"DELETE_AGENT",payload:N})}catch(m){throw console.error("Failed to delete agent:",m),m}},o=N=>{n({type:"START_DISCUSSION",payload:N})},a=N=>{n({type:"SET_CURRENT_DISCUSSION",payload:N})},c=async N=>{if(t.currentDiscussion)try{const m={...t.currentDiscussion,status:"ended",endReason:N||"手动终止"};await O.saveDiscussion(m),n({type:"END_DISCUSSION"});const d=await O.getDiscussions();n({type:"SET_ALL_DISCUSSIONS",payload:d})}catch(m){console.error("Failed to save discussion:",m),n({type:"END_DISCUSSION"})}else n({type:"END_DISCUSSION"})},f=async(N,m,d)=>{const u={id:gi(),agentId:m,content:N,type:d,timestamp:new Date};if(n({type:"ADD_MESSAGE",payload:u}),t.currentDiscussion)try{await V.addMessage(t.currentDiscussion.id,u)}catch(p){console.error("Failed to save message:",p)}},x=N=>{n({type:"UPDATE_SETTINGS",payload:N})},g=N=>{n({type:"UPDATE_PREFERENCES",payload:N})},y=async()=>{try{const[N,m,d,u]=await Promise.all([O.getAgents(),O.getDiscussions(),O.getSettings(),O.getPreferences()]);return{agents:N||[],discussions:m||[],settings:d||{},preferences:u||{}}}catch(N){throw console.error("Failed to load data:",N),N}},v=async()=>{try{const N=await O.getAllData();return JSON.stringify(N,null,2)}catch(N){throw console.error("Failed to export data:",N),new Error("导出数据失败")}},w=async N=>{try{const m=JSON.parse(N);if(!O.validateData(m))throw new Error("数据格式无效");await O.importAllData(m);const d=await y();return n({type:"INITIALIZE_SUCCESS",payload:d}),!0}catch(m){return console.error("Failed to import data:",m),!1}},h=async()=>{try{await O.clearAllData(),n({type:"INITIALIZE_SUCCESS",payload:{agents:[],discussions:[],settings:{},preferences:{}}})}catch(N){throw console.error("Failed to clear data:",N),new Error("清除数据失败")}};return s.jsx(Gu.Provider,{value:{state:t,dispatch:n,addAgent:r,updateAgent:l,deleteAgent:i,startDiscussion:o,setCurrentDiscussion:a,endDiscussion:c,sendMessage:f,updateSettings:x,updatePreferences:g,exportData:v,importData:w,clearAllData:h},children:e})}const At=()=>{const e=T.useContext(Gu);if(!e)throw new Error("useApp must be used within AppProvider");return e};var Vf={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Hf=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Bf=(e,t)=>{const n=T.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,children:a,...c},f)=>T.createElement("svg",{ref:f,...Vf,width:l,height:l,stroke:r,strokeWidth:o?Number(i)*24/Number(l):i,className:`lucide lucide-${Hf(e)}`,...c},[...t.map(([x,g])=>T.createElement(x,g)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${e}`,n};var _=Bf;const Dn=_("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),Es=_("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),wl=_("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),bt=_("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),Wf=_("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),et=_("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),Qf=_("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),Kf=_("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Ls=_("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),qu=_("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]),go=_("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),xi=_("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),Gf=_("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]),qf=_("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),Xf=_("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Yf=_("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),Zf=_("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),Xu=_("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),Ts=_("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),Jf=_("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),Ea=_("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]]),La=_("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),He=_("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),Yu=_("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),ep=_("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),yi=_("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),tp=_("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),np=_("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),rp=_("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),Ct=_("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Ta=_("StopCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{width:"6",height:"6",x:"9",y:"9",key:"1wrtvo"}]]),sp=_("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),lp=_("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),Zu=_("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]]),Ju=_("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),yr=_("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),ed=_("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),vi=_("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),ip=_("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]),op=_("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),rt=_("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),ap=_("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]),cp=_("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Da=_("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),up=[{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"OpenAI",model:"gpt-4-turbo-preview",description:"OpenAI最新的GPT-4 Turbo模型，性能强大，适合复杂推理",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-4",name:"GPT-4",provider:"OpenAI",model:"gpt-4",description:"OpenAI的旗舰模型，适合需要高质量输出的场景",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"OpenAI",model:"gpt-3.5-turbo",description:"快速且经济的模型，适合大多数对话任务",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-opus",name:"Claude 3 Opus",provider:"Anthropic",model:"claude-3-opus-20240229",description:"Anthropic最强大的模型，擅长复杂分析和创作",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"claude-3-sonnet",name:"Claude 3 Sonnet",provider:"Anthropic",model:"claude-3-sonnet-20240229",description:"平衡性能和成本的优秀选择",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-haiku",name:"Claude 3 Haiku",provider:"Anthropic",model:"claude-3-haiku-20240307",description:"快速响应的轻量级模型，适合简单任务",defaultSettings:{temperature:.7,maxTokens:600}},{id:"gpt-4-azure",name:"Azure GPT-4",provider:"Azure",model:"gpt-4",description:"部署在Azure上的GPT-4模型",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-35-turbo-azure",name:"Azure GPT-3.5 Turbo",provider:"Azure",model:"gpt-35-turbo",description:"部署在Azure上的GPT-3.5 Turbo模型",defaultSettings:{temperature:.7,maxTokens:800}}],Ma=up;function Ds(e){return{openai:"🤖",anthropic:"🧠",azure:"☁️",custom:"⚙️"}[e.toLowerCase()]||"🔧"}function Ms(e){return{openai:"bg-green-100 text-green-800",anthropic:"bg-blue-100 text-blue-800",azure:"bg-purple-100 text-purple-800",custom:"bg-gray-100 text-gray-800"}[e.toLowerCase()]||"bg-gray-100 text-gray-800"}const Aa=["/images/agent-alex.jpg","/images/agent-luna.jpg","/images/agent-max.jpg","/images/agent-chen.jpg","/images/agent-sam.jpg","/images/agent-robin.jpg","/images/agent-taylor.jpg","/images/agent-zoe.jpg"],dp=["技术","商业","设计","营销","数据分析","产品管理","法律","心理学","教育","医疗"],td=[{value:"logical",label:"逻辑型"},{value:"creative",label:"创意型"},{value:"analytical",label:"分析型"},{value:"intuitive",label:"直觉型"},{value:"systematic",label:"系统型"}],mp=[{value:"assertive",label:"果断型"},{value:"collaborative",label:"协作型"},{value:"diplomatic",label:"外交型"},{value:"direct",label:"直接型"},{value:"thoughtful",label:"深思型"}],fp=["数据查询","市场分析","技术调研","用户调研","竞品分析","风险评估","法律咨询","创意生成"];function pp(){const{state:e,addAgent:t,updateAgent:n,deleteAgent:r}=At(),[l,i]=T.useState(!1),[o,a]=T.useState(null),c=async x=>{if(confirm("确定要删除这个智能体吗？"))try{await r(x)}catch(g){alert("删除智能体失败: "+(g instanceof Error?g.message:"未知错误"))}},f=x=>{o?(n({...x,id:o.id,isActive:o.isActive}),a(null)):(t(x),i(!1))};return s.jsx("div",{className:"h-full bg-gradient-to-br from-slate-50 to-blue-50 w-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsxs("div",{className:"centered-content",children:[s.jsxs("div",{className:"flex items-center justify-between mb-8",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"智能体管理"}),s.jsx("p",{className:"text-gray-600",children:"配置和管理您的AI智能体团队"})]}),s.jsxs("button",{onClick:()=>i(!0),className:"flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-lg",children:[s.jsx(yi,{size:20}),"添加智能体"]})]}),s.jsx("div",{className:"flex flex-wrap gap-6 mb-8",children:e.agents.map(x=>s.jsx(hp,{agent:x,onEdit:()=>a(x),onDelete:()=>c(x.id)},x.id))}),(l||o)&&s.jsx(gp,{agent:o,onSubmit:f,onCancel:()=>{i(!1),a(null)}})]})})})}function hp({agent:e,onEdit:t,onDelete:n}){var r,l,i;return s.jsxs("div",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[s.jsx("img",{src:e.avatar,alt:e.name,className:"w-16 h-16 rounded-full object-cover border-4 border-blue-100"}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:e.name}),s.jsx("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:e.isActive?"活跃":"非活跃"})]})]}),s.jsxs("div",{className:"space-y-3 mb-4",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(op,{size:16,className:"text-blue-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"专业领域："}),s.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.expertise.slice(0,2).map((o,a)=>s.jsx("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded",children:o},a)),e.expertise.length>2&&s.jsxs("span",{className:"text-xs text-gray-500",children:["+",e.expertise.length-2]})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(bt,{size:16,className:"text-purple-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"思维方式："}),s.jsx("span",{className:"text-sm font-medium text-gray-900",children:(r=td.find(o=>o.value===e.thinkingStyle))==null?void 0:r.label})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(ap,{size:16,className:"text-green-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"工具："}),s.jsxs("span",{className:"text-sm text-gray-500",children:[e.tools.length," 个"]})]}),e.llmConfig&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Ct,{size:16,className:"text-orange-600"}),s.jsx("span",{className:"text-sm text-gray-600",children:"LLM："}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("span",{className:"text-sm",children:Ds(e.llmConfig.provider)}),s.jsx("span",{className:`text-xs px-2 py-0.5 rounded ${Ms(e.llmConfig.provider)}`,children:e.llmConfig.name})]})]}),e.isModerator&&s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-4 h-4 bg-purple-600 rounded-full flex items-center justify-center",children:s.jsx("span",{className:"text-white text-xs font-bold",children:"主"})}),s.jsx("span",{className:"text-sm text-gray-600",children:"可做主持人"}),s.jsx("span",{className:"text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded",children:((l=e.moderatorConfig)==null?void 0:l.managementStyle)==="strict"?"严格型":((i=e.moderatorConfig)==null?void 0:i.managementStyle)==="flexible"?"灵活型":"协作型"})]})]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("button",{onClick:t,className:"flex-1 bg-blue-50 text-blue-600 py-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center gap-1",children:[s.jsx(Yu,{size:16}),"编辑"]}),s.jsxs("button",{onClick:n,className:"flex-1 bg-red-50 text-red-600 py-2 rounded-lg hover:bg-red-100 transition-colors flex items-center justify-center gap-1",children:[s.jsx(yr,{size:16}),"删除"]})]})]})}function gp({agent:e,onSubmit:t,onCancel:n}){var y;const[r,l]=T.useState({name:(e==null?void 0:e.name)||"",avatar:(e==null?void 0:e.avatar)||Aa[0],expertise:(e==null?void 0:e.expertise)||[],thinkingStyle:(e==null?void 0:e.thinkingStyle)||"logical",personality:(e==null?void 0:e.personality)||"collaborative",tools:(e==null?void 0:e.tools)||[],llmConfig:(e==null?void 0:e.llmConfig)||null,isModerator:(e==null?void 0:e.isModerator)||!1,moderatorConfig:(e==null?void 0:e.moderatorConfig)||{summaryFrequency:5,interventionThreshold:.6,managementStyle:"flexible",autoTerminate:!0,maxInterventions:5}}),[i,o]=T.useState([]),[a,c]=T.useState(!0);T.useEffect(()=>{(async()=>{try{c(!0);const w=await O.getLLMConfigs();o(w)}catch(w){console.error("Failed to load LLM configs:",w),o([])}finally{c(!1)}})()},[]);const f=v=>{v.preventDefault(),r.name&&r.expertise.length>0&&r.llmConfig&&t({...r,llmConfig:r.llmConfig,isModerator:r.isModerator,moderatorConfig:r.isModerator?r.moderatorConfig:void 0})},x=v=>{l(w=>({...w,expertise:w.expertise.includes(v)?w.expertise.filter(h=>h!==v):[...w.expertise,v]}))},g=v=>{l(w=>({...w,tools:w.tools.includes(v)?w.tools.filter(h=>h!==v):[...w.tools,v]}))};return s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[s.jsx("div",{className:"p-6 border-b border-gray-200",children:s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:e?"编辑智能体":"添加新智能体"})}),s.jsxs("form",{onSubmit:f,className:"p-6 space-y-6",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"智能体名称"}),s.jsx("input",{type:"text",value:r.name,onChange:v=>l(w=>({...w,name:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入智能体名称",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择头像"}),s.jsx("div",{className:"grid grid-cols-8 gap-3",children:Aa.map((v,w)=>s.jsx("button",{type:"button",onClick:()=>l(h=>({...h,avatar:v})),className:`relative rounded-lg overflow-hidden border-4 transition-all ${r.avatar===v?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300"}`,children:s.jsx("img",{src:v,alt:`Avatar ${w+1}`,className:"w-16 h-16 object-cover"})},w))})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"专业领域（至少选择一个）"}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:dp.map(v=>s.jsx("button",{type:"button",onClick:()=>x(v),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.expertise.includes(v)?"bg-blue-100 text-blue-800 border-2 border-blue-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:v},v))})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"思维方式"}),s.jsx("select",{value:r.thinkingStyle,onChange:v=>l(w=>({...w,thinkingStyle:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:td.map(v=>s.jsx("option",{value:v.value,children:v.label},v.value))})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"性格特征"}),s.jsx("select",{value:r.personality,onChange:v=>l(w=>({...w,personality:v.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:mp.map(v=>s.jsx("option",{value:v.value,children:v.label},v.value))})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"可用工具"}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:fp.map(v=>s.jsx("button",{type:"button",onClick:()=>g(v),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.tools.includes(v)?"bg-green-100 text-green-800 border-2 border-green-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:v},v))})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["LLM配置 ",s.jsx("span",{className:"text-red-500",children:"*"})]}),a?s.jsxs("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 flex items-center gap-2",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),s.jsx("span",{className:"text-gray-600",children:"正在加载LLM配置..."})]}):s.jsxs("select",{value:((y=r.llmConfig)==null?void 0:y.id)||"",onChange:v=>{const w=i.find(h=>h.id===v.target.value);l(h=>({...h,llmConfig:w||null}))},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0,children:[s.jsx("option",{value:"",children:"请选择LLM配置"}),i.map(v=>s.jsxs("option",{value:v.id,children:[Ds(v.provider)," ",v.name," (",v.provider.toUpperCase(),")"]},v.id))]}),r.llmConfig&&s.jsxs("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx("span",{className:"text-sm font-medium text-gray-700",children:"已选择:"}),s.jsx("span",{className:`text-xs px-2 py-1 rounded ${Ms(r.llmConfig.provider)}`,children:r.llmConfig.name})]}),s.jsxs("div",{className:"text-xs text-gray-600",children:["模型: ",r.llmConfig.model," | 温度: ",r.llmConfig.temperature," | 令牌: ",r.llmConfig.maxTokens]})]}),i.length===0&&s.jsx("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:s.jsxs("p",{className:"text-sm text-yellow-800",children:["暂无可用的LLM配置。请先前往",s.jsx("span",{className:"font-medium",children:"LLM配置"}),"页面创建LLM配置，然后再创建智能体。"]})})]}),s.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("input",{type:"checkbox",id:"isModerator",checked:r.isModerator,onChange:v=>l(w=>({...w,isModerator:v.target.checked})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),s.jsx("label",{htmlFor:"isModerator",className:"text-sm font-medium text-gray-700",children:"可以做主持人"})]}),r.isModerator&&s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-4",children:[s.jsx("h4",{className:"font-medium text-blue-900 mb-3",children:"主持人配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"总结频率（每N条消息）"}),s.jsx("input",{type:"number",min:"1",max:"20",value:r.moderatorConfig.summaryFrequency,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,summaryFrequency:parseInt(v.target.value)||5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"干预阈值（0-1）"}),s.jsx("input",{type:"number",min:"0",max:"1",step:"0.1",value:r.moderatorConfig.interventionThreshold,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,interventionThreshold:parseFloat(v.target.value)||.6}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"管理风格"}),s.jsxs("select",{value:r.moderatorConfig.managementStyle,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,managementStyle:v.target.value}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"strict",children:"严格型"}),s.jsx("option",{value:"flexible",children:"灵活型"}),s.jsx("option",{value:"collaborative",children:"协作型"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大干预次数"}),s.jsx("input",{type:"number",min:"1",max:"20",value:r.moderatorConfig.maxInterventions,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,maxInterventions:parseInt(v.target.value)||5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("input",{type:"checkbox",id:"autoTerminate",checked:r.moderatorConfig.autoTerminate,onChange:v=>l(w=>({...w,moderatorConfig:{...w.moderatorConfig,autoTerminate:v.target.checked}})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),s.jsx("label",{htmlFor:"autoTerminate",className:"text-sm font-medium text-gray-700",children:"自动终止讨论"})]})]})]}),s.jsxs("div",{className:"flex gap-3 pt-4 border-t border-gray-200",children:[s.jsx("button",{type:"button",onClick:n,className:"flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"取消"}),s.jsx("button",{type:"submit",disabled:a||i.length===0,className:"flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:a?"加载中...":`${e?"更新":"创建"}智能体`})]})]})]})})}function xp(){var x,g,y,v,w;const{state:e,startDiscussion:t}=At(),[n,r]=T.useState({topic:"",mode:"free",selectedAgents:[],moderatorId:void 0}),[l,i]=T.useState([]),o=()=>{const h=[];return n.topic.trim()||h.push("请输入讨论话题"),n.selectedAgents.length<2&&h.push("至少需要选择2个智能体参与讨论"),n.selectedAgents.length>8&&h.push("最多支持8个智能体同时讨论"),n.mode==="moderator"&&!n.moderatorId&&(n.selectedAgents.map(m=>f.find(d=>d.id===m)).filter(m=>m&&m.isModerator).length===0?h.push("主持人模式需要至少一个具备主持人能力的智能体"):h.push("主持人模式下请选择一个主持人")),i(h),h.length===0},a=()=>{o()&&t(n)},c=h=>{r(N=>({...N,selectedAgents:N.selectedAgents.includes(h)?N.selectedAgents.filter(m=>m!==h):[...N.selectedAgents,h]}))},f=e.agents.filter(h=>h.isActive);return f.length===0?s.jsx("div",{className:"h-full bg-gradient-to-br from-orange-50 to-red-50 overflow-y-auto",children:s.jsx("div",{className:"max-w-4xl mx-auto p-6",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center",children:[s.jsx(Dn,{size:64,className:"text-orange-500 mx-auto mb-4"}),s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有可用的智能体"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"您需要先创建和配置智能体才能开始讨论。"}),s.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:"前往智能体管理"})]})})}):s.jsx("div",{className:"h-full bg-gradient-to-br from-purple-50 to-pink-50 w-full overflow-y-auto",children:s.jsx("div",{className:"flex justify-center p-6",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",style:{width:"800px",maxWidth:"800px"},children:[s.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(La,{size:32}),s.jsx("h1",{className:"text-3xl font-bold",children:"创建新讨论"})]}),s.jsx("p",{className:"text-purple-100",children:"配置讨论话题、模式和参与者，开始智能体之间的协作讨论"})]}),s.jsxs("div",{className:"p-8 space-y-8",children:[l.length>0&&s.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx(Dn,{size:20,className:"text-red-600"}),s.jsx("h3",{className:"font-medium text-red-800",children:"配置错误"})]}),s.jsx("ul",{className:"text-red-700 text-sm space-y-1",children:l.map((h,N)=>s.jsxs("li",{children:["• ",h]},N))})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论话题"}),s.jsx("textarea",{value:n.topic,onChange:h=>r(N=>({...N,topic:h.target.value})),placeholder:"请输入您想要讨论的话题，例如：如何提升用户体验设计质量？",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none",rows:3}),s.jsx("p",{className:"text-sm text-gray-500",children:"清晰的话题描述有助于智能体更好地理解和参与讨论"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论模式"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("button",{onClick:()=>r(h=>({...h,mode:"free"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="free"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx(La,{size:24,className:n.mode==="free"?"text-purple-600":"text-gray-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"自由讨论模式"})]}),s.jsx("p",{className:"text-gray-600 text-sm",children:"智能体根据话题相关性和兴趣自主发言，讨论更加自然流畅"})]}),s.jsxs("button",{onClick:()=>r(h=>({...h,mode:"moderator"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="moderator"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx(Ct,{size:24,className:n.mode==="moderator"?"text-purple-600":"text-gray-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"主持人模式"})]}),s.jsx("p",{className:"text-gray-600 text-sm",children:"选择一个智能体作为主持人，按轮次组织讨论，更加有序规范"})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(rt,{size:24,className:"text-purple-600"}),s.jsxs("h2",{className:"text-lg font-semibold text-gray-900",children:["选择参与者 (",n.selectedAgents.length,"/8)"]})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:f.map(h=>s.jsxs("button",{onClick:()=>c(h.id),className:`p-4 rounded-xl border-2 transition-all text-left ${n.selectedAgents.includes(h.id)?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsx("img",{src:h.avatar,alt:h.name,className:"w-10 h-10 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("h3",{className:"font-medium text-gray-900",children:h.name}),s.jsx("p",{className:"text-sm text-gray-500",children:h.expertise.slice(0,2).join("、")})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:[h.thinkingStyle==="logical"&&"逻辑型",h.thinkingStyle==="creative"&&"创意型",h.thinkingStyle==="analytical"&&"分析型",h.thinkingStyle==="intuitive"&&"直觉型",h.thinkingStyle==="systematic"&&"系统型"]}),h.isModerator&&s.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:"主持人"})]}),n.selectedAgents.includes(h.id)&&s.jsx("div",{className:"w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center",children:s.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})})]})]},h.id))}),s.jsx("p",{className:"text-sm text-gray-500",children:"建议选择具有不同专业背景和思维方式的智能体，以获得更丰富的讨论视角"})]}),n.selectedAgents.length>0&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ct,{size:24,className:"text-purple-600"}),s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"主持人设置"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"选择主持人（可选）"}),s.jsxs("select",{value:n.moderatorId||"",onChange:h=>r(N=>({...N,moderatorId:h.target.value||void 0})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:"",children:"无主持人（系统自动管理）"}),n.selectedAgents.map(h=>f.find(N=>N.id===h)).filter(h=>h&&h.isModerator).map(h=>s.jsxs("option",{value:h.id,children:[h.name," - ",h.expertise.slice(0,2).join("、")]},h.id))]}),n.moderatorId&&s.jsxs("div",{className:"space-y-4",children:[(()=>{var N,m,d;const h=f.find(u=>u.id===n.moderatorId);return h?s.jsx("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start gap-4",children:[s.jsx("img",{src:h.avatar,alt:h.name,className:"w-12 h-12 rounded-full object-cover"}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:h.name}),s.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:"主持人"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"专业领域："}),s.jsx("span",{className:"text-gray-900",children:h.expertise.slice(0,3).join("、")})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"思维方式："}),s.jsxs("span",{className:"text-gray-900",children:[h.thinkingStyle==="logical"&&"逻辑型",h.thinkingStyle==="creative"&&"创意型",h.thinkingStyle==="analytical"&&"分析型",h.thinkingStyle==="intuitive"&&"直觉型",h.thinkingStyle==="systematic"&&"系统型"]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"性格特征："}),s.jsxs("span",{className:"text-gray-900",children:[h.personality==="assertive"&&"果断型",h.personality==="collaborative"&&"协作型",h.personality==="diplomatic"&&"外交型",h.personality==="direct"&&"直接型",h.personality==="thoughtful"&&"深思型"]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"管理风格："}),s.jsxs("span",{className:"text-gray-900",children:[((N=n.moderatorConfig)==null?void 0:N.managementStyle)==="strict"&&"严格型",((m=n.moderatorConfig)==null?void 0:m.managementStyle)==="flexible"&&"灵活型",((d=n.moderatorConfig)==null?void 0:d.managementStyle)==="collaborative"&&"协作型"]})]})]})]})]})}):null})(),s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:s.jsx(Ct,{size:16,className:"text-blue-600"})}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium text-blue-900 mb-1",children:"主持人职责"}),s.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[s.jsx("li",{children:"• 实时总结讨论内容"}),s.jsx("li",{children:"• 监控话题相关性"}),s.jsx("li",{children:"• 指定发言顺序（主持人模式）"}),s.jsx("li",{children:"• 引导讨论方向"})]})]})]})}),s.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"主持人配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"总结频率"}),s.jsxs("select",{value:((x=n.moderatorConfig)==null?void 0:x.summaryFrequency)||5,onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:parseInt(h.target.value),interventionThreshold:((m=N.moderatorConfig)==null?void 0:m.interventionThreshold)||.6,managementStyle:((d=N.moderatorConfig)==null?void 0:d.managementStyle)||"flexible",autoTerminate:((u=N.moderatorConfig)==null?void 0:u.autoTerminate)||!0,maxInterventions:((p=N.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:3,children:"每3条消息"}),s.jsx("option",{value:5,children:"每5条消息"}),s.jsx("option",{value:8,children:"每8条消息"}),s.jsx("option",{value:10,children:"每10条消息"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"管理风格"}),s.jsxs("select",{value:((g=n.moderatorConfig)==null?void 0:g.managementStyle)||"flexible",onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:((m=N.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:((d=N.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:h.target.value,autoTerminate:((u=N.moderatorConfig)==null?void 0:u.autoTerminate)||!0,maxInterventions:((p=N.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:"strict",children:"严格型 - 严格控制发言顺序"}),s.jsx("option",{value:"flexible",children:"灵活型 - 适度引导讨论"}),s.jsx("option",{value:"collaborative",children:"协作型 - 鼓励自由交流"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"干预阈值"}),s.jsxs("select",{value:((y=n.moderatorConfig)==null?void 0:y.interventionThreshold)||.6,onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:((m=N.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:parseFloat(h.target.value),managementStyle:((d=N.moderatorConfig)==null?void 0:d.managementStyle)||"flexible",autoTerminate:((u=N.moderatorConfig)==null?void 0:u.autoTerminate)||!0,maxInterventions:((p=N.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:.8,children:"高敏感度 - 轻微偏题即干预"}),s.jsx("option",{value:.6,children:"中等敏感度 - 适度偏题时干预"}),s.jsx("option",{value:.4,children:"低敏感度 - 严重偏题才干预"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大干预次数"}),s.jsxs("select",{value:((v=n.moderatorConfig)==null?void 0:v.maxInterventions)||5,onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:((m=N.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:((d=N.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:((u=N.moderatorConfig)==null?void 0:u.managementStyle)||"flexible",autoTerminate:((p=N.moderatorConfig)==null?void 0:p.autoTerminate)||!0,maxInterventions:parseInt(h.target.value)}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[s.jsx("option",{value:3,children:"3次"}),s.jsx("option",{value:5,children:"5次"}),s.jsx("option",{value:8,children:"8次"}),s.jsx("option",{value:-1,children:"不限制"})]})]})]}),s.jsx("div",{className:"mt-4",children:s.jsxs("label",{className:"flex items-center gap-2",children:[s.jsx("input",{type:"checkbox",checked:((w=n.moderatorConfig)==null?void 0:w.autoTerminate)||!0,onChange:h=>r(N=>{var m,d,u,p;return{...N,moderatorConfig:{...N.moderatorConfig,summaryFrequency:((m=N.moderatorConfig)==null?void 0:m.summaryFrequency)||5,interventionThreshold:((d=N.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:((u=N.moderatorConfig)==null?void 0:u.managementStyle)||"flexible",autoTerminate:h.target.checked,maxInterventions:((p=N.moderatorConfig)==null?void 0:p.maxInterventions)||5}}}),className:"w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"}),s.jsx("span",{className:"text-sm text-gray-700",children:"允许主持人自动终止讨论"})]})})]})]}),s.jsx("p",{className:"text-sm text-gray-500",children:"主持人将负责管理讨论流程，确保讨论高效有序进行"})]})]}),s.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"高级设置（可选）"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大消息数量"}),s.jsx("input",{type:"number",min:"10",max:"100",value:n.maxMessages||"",onChange:h=>r(N=>({...N,maxMessages:h.target.value?parseInt(h.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"时间限制（分钟）"}),s.jsx("input",{type:"number",min:"5",max:"120",value:n.timeLimit||"",onChange:h=>r(N=>({...N,timeLimit:h.target.value?parseInt(h.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]})]})]}),s.jsx("div",{className:"flex justify-center pt-6",children:s.jsxs("button",{onClick:a,disabled:!n.topic.trim()||n.selectedAgents.length<2,className:"flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all shadow-lg text-lg font-medium",children:[s.jsx(ep,{size:24}),"开始讨论"]})})]})]})})})}const Ut=class Ut{static getInstance(){return Ut.instance||(Ut.instance=new Ut),Ut.instance}async callLLM(t,n){try{return await this.makeAPICall(t,n)}catch(r){throw console.error("LLM API调用失败:",r),new Error(`LLM调用失败: ${r instanceof Error?r.message:"未知错误"}`)}}async generateResponse(t,n){const r={messages:[{role:"user",content:n}],temperature:t.temperature||.7,maxTokens:t.maxTokens||500,model:t.model};return(await this.callLLM(t,r)).content}async generateAgentMessage(t,n,r,l=[],i=!1){const o=this.buildSystemPrompt(t,r,i),a=this.buildConversationHistory(l,t.id),c={messages:[{role:"system",content:o},...a,{role:"user",content:`请基于当前讨论情况，就"${r}"这个话题发表你的观点。`}],temperature:t.llmConfig.temperature||.7,maxTokens:t.llmConfig.maxTokens||500,model:t.llmConfig.model};return(await this.callLLM(t.llmConfig,c)).content}buildSystemPrompt(t,n,r=!1){const l=r?`你是一个名为"${t.name}"的智能体，正在作为主持人主持关于"${n}"的讨论。`:`你是一个名为"${t.name}"的智能体，正在参与关于"${n}"的讨论。`,i=r?`作为主持人，你的职责包括：
1. 引导讨论方向，确保讨论围绕主题进行
2. 平衡各方观点，促进建设性对话
3. 适时总结要点，推进讨论进展
4. 维持讨论秩序，化解可能的冲突
5. 在适当时机推动达成共识`:`请根据你的专业背景和性格特征参与讨论，保持角色一致性。你的回复应该：
1. 体现你的专业知识和思维方式
2. 符合你的性格特征
3. 简洁明了，通常在100字以内
4. 针对讨论话题提供有价值的新观点`;return`${l}

你的特征：
- 专业领域：${t.expertise.join("、")}
- 思维方式：${t.thinkingStyle}
- 性格特征：${t.personality}
- 可用工具：${t.tools.join("、")}

${i}

${t.llmConfig.systemPrompt||""}`}buildConversationHistory(t,n){return t.slice(-10).map(l=>({role:l.agentId===n?"assistant":"user",content:`${l.agentId===n?"我":"其他参与者"}：${l.content}`}))}async makeAPICall(t,n){const r=this.getAPIUrl(t),l=this.getAPIHeaders(t),i=this.formatRequestBody(t,n),o=await fetch(r,{method:"POST",headers:l,body:JSON.stringify(i)});if(!o.ok)throw new Error(`API请求失败: ${o.status} ${o.statusText}`);const a=await o.json();return this.parseResponse(t,a)}getAPIUrl(t){if(t.baseURL)return`${t.baseURL}/chat/completions`;switch(t.provider){case"openai":return"https://api.openai.com/v1/chat/completions";case"anthropic":return"https://api.anthropic.com/v1/messages";case"azure":return`${t.baseURL}/openai/deployments/${t.model}/chat/completions?api-version=2023-12-01-preview`;default:throw new Error(`不支持的提供商: ${t.provider}`)}}getAPIHeaders(t){const n={"Content-Type":"application/json"};switch(t.provider){case"openai":case"azure":case"custom":n.Authorization=`Bearer ${t.apiKey}`;break;case"anthropic":n["x-api-key"]=t.apiKey,n["anthropic-version"]="2023-06-01";break}return n}formatRequestBody(t,n){var r;switch(t.provider){case"anthropic":return{model:n.model||t.model,max_tokens:n.maxTokens||1e3,temperature:n.temperature||.7,messages:n.messages.filter(l=>l.role!=="system"),system:((r=n.messages.find(l=>l.role==="system"))==null?void 0:r.content)||""};default:return{model:n.model||t.model,messages:n.messages,temperature:n.temperature||.7,max_tokens:n.maxTokens||1e3}}}parseResponse(t,n){var r,l,i;switch(t.provider){case"anthropic":return{content:((r=n.content[0])==null?void 0:r.text)||"",usage:n.usage?{promptTokens:n.usage.input_tokens,completionTokens:n.usage.output_tokens,totalTokens:n.usage.input_tokens+n.usage.output_tokens}:void 0,model:n.model};default:return{content:((i=(l=n.choices[0])==null?void 0:l.message)==null?void 0:i.content)||"",usage:n.usage?{promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens,totalTokens:n.usage.total_tokens}:void 0,model:n.model}}}async testLLMConfig(t){try{const n={messages:[{role:"user",content:'请回复"测试成功"'}],temperature:.1,maxTokens:10};return await this.callLLM(t,n),!0}catch(n){return console.error("LLM配置测试失败:",n),!1}}};Ae(Ut,"instance");let ji=Ut;const Jt=ji.getInstance();async function yp(e,t,n,r=[],l=!1){if(!e.llmConfig)throw new Error(`智能体 ${e.name} 未配置LLM，无法生成消息`);try{return await Jt.generateAgentMessage(e,t,n,r,l)}catch(i){throw console.error(`智能体 ${e.name} 的LLM调用失败:`,i),new Error(`智能体 ${e.name} 的LLM调用失败: ${i instanceof Error?i.message:"未知错误"}`)}}function vp(e,t){if(e.length<3)return 0;const n=e.slice(-10),r=n.filter(g=>g.type==="agreement").length/n.length,l=n.filter(g=>g.type==="disagreement").length/n.length,i=n.filter(g=>g.type==="question").length/n.length,o=new Map;n.forEach(g=>{o.set(g.agentId,(o.get(g.agentId)||0)+1)});const a=o.size,c=t.filter(g=>g.isActive).length,f=a/c;let x=0;return x+=r*40,x+=(1-l)*30,x+=i>.1&&i<.3?15:0,x+=f*15,Math.min(100,Math.max(0,x))}function jp(e,t){const r=e.slice(-15).filter(i=>i.type==="statement"||i.type==="agreement");if(r.length===0)return`关于"${t}"，参与者需要更多时间来达成共识。`;const l=wp(r.map(i=>i.content));return`经过充分讨论，大家就"${t}"达成了共识：${l.slice(0,3).join("、")}是关键要素，需要重点关注和实施。`}function wp(e){return["技术创新","用户体验","市场需求","成本控制","时间规划","质量保证","团队合作","数据分析"].filter(()=>Math.random()>.6).slice(0,5)}function ns(e,t,n){if(e.length===0)return null;if(n==="moderator"){const r=t.slice(-e.length).map(i=>i.agentId),l=e.filter(i=>!r.includes(i));return l.length>0?l[0]:e[0]}else{const r=t.slice(-10),l=new Map;e.forEach(c=>l.set(c,0)),r.forEach(c=>{e.includes(c.agentId)&&l.set(c.agentId,(l.get(c.agentId)||0)+1)});const o=[...l.entries()].sort(([,c],[,f])=>c-f).slice(0,Math.ceil(e.length/2));return o[Math.floor(Math.random()*o.length)][0]}}async function Np(e,t,n){if(!e.llmConfig)throw new Error(`主持人 ${e.name} 未配置LLM，无法生成总结`);try{const r=`作为讨论主持人，请对以下最近的讨论内容进行简洁总结：

讨论主题：${t.topic}
最近消息：
${n.map(l=>`- ${l.content}`).join(`
`)}

请提供一个简洁的总结，突出关键观点和进展：`;return await Jt.generateResponse(e.llmConfig,r)}catch(r){return console.error("主持人总结生成失败:",r),`总结生成失败：${r instanceof Error?r.message:"未知错误"}`}}async function Sp(e,t,n){if(!e.llmConfig)return .8;try{const r=`作为讨论主持人，请评估以下讨论内容与主题的相关性：

讨论主题：${t.topic}
最近消息：
${n.map(o=>`- ${o.content}`).join(`
`)}

请给出相关性评分（0-1之间的数字，1表示完全相关，0表示完全无关）。
只需要返回数字，不需要解释：`,l=await Jt.generateResponse(e.llmConfig,r),i=parseFloat(l.trim());return isNaN(i)?.8:Math.max(0,Math.min(1,i))}catch(r){return console.error("话题相关性计算失败:",r),.8}}async function kp(e,t,n,r){if(!e.llmConfig||n.length===0)return ns(n.map(l=>l.id),r,t.mode);try{const l=n.map(f=>`${f.name}（专业：${f.expertise.join("、")}，思维：${f.thinkingStyle}）`).join(`
`),i=`作为讨论主持人，请根据以下信息选择下一个最适合发言的参与者：

讨论主题：${t.topic}
参与者信息：
${l}

最近讨论内容：
${r.slice(-5).map(f=>{const x=n.find(g=>g.id===f.agentId);return`${(x==null?void 0:x.name)||"未知"}: ${f.content}`}).join(`
`)}

请选择最适合继续这个话题的参与者，只需要返回参与者的名字：`,a=(await Jt.generateResponse(e.llmConfig,i)).trim(),c=n.find(f=>f.name===a);return(c==null?void 0:c.id)||ns(n.map(f=>f.id),r,t.mode)}catch(l){return console.error("智能发言人选择失败:",l),ns(n.map(i=>i.id),r,t.mode)}}async function bp(e,t,n){if(!e.llmConfig)return{off_topic:"让我们回到主题上来，继续讨论相关内容。",low_activity:"大家可以分享更多想法，让讨论更加活跃。",conflict_resolution:"我们来总结一下不同的观点，寻找共同点。",summary_request:"让我总结一下到目前为止的讨论要点。"}[n];try{const r={off_topic:`作为讨论主持人，发现讨论偏离了主题"${t.topic}"。请生成一段引导语，礼貌地将讨论拉回正轨。`,low_activity:"作为讨论主持人，发现讨论活跃度较低。请生成一段鼓励性的引导语，激发参与者的讨论热情。",conflict_resolution:"作为讨论主持人，发现参与者之间存在分歧。请生成一段中性的引导语，帮助化解冲突并推进讨论。",summary_request:"作为讨论主持人，需要对当前讨论进行阶段性总结。请生成一段总结性的引导语。"};return(await Jt.generateResponse(e.llmConfig,r[n])).trim()}catch(r){return console.error("主持人引导语生成失败:",r),"让我们继续讨论，保持专注和建设性。"}}async function Cp(e,t,n){if(t.consensusScore>85)return{shouldTerminate:!0,reason:"达成共识"};if(t.moderatorInterventions>=n.maxInterventions&&n.maxInterventions>0)return{shouldTerminate:!0,reason:"主持人终止"};if(!n.autoTerminate||!e.llmConfig)return{shouldTerminate:!1};try{const r=t.messages.slice(-10),l=`作为讨论主持人，请判断以下讨论是否应该结束：

讨论主题：${t.topic}
当前共识度：${t.consensusScore}%
话题相关性：${t.topicRelevanceScore}
干预次数：${t.moderatorInterventions}

最近讨论内容：
${r.map(a=>`- ${a.content}`).join(`
`)}

请判断是否应该结束讨论，只需要回答"是"或"否"：`,o=(await Jt.generateResponse(e.llmConfig,l)).trim().toLowerCase().includes("是");return{shouldTerminate:o,reason:o?"主持人终止":void 0}}catch(r){return console.error("讨论终止判断失败:",r),{shouldTerminate:!1}}}function Ep(){var en,E;const{state:e,dispatch:t,endDiscussion:n,sendMessage:r,setCurrentDiscussion:l}=At(),[i,o]=T.useState(!1),[a,c]=T.useState(null),[f,x]=T.useState(!1),[g,y]=T.useState({totalMessages:0,consensusScore:0,activeTime:0}),[v,w]=T.useState(""),[h,N]=T.useState(1),[m,d]=T.useState(0),u=T.useRef(null),p=T.useRef(null),j=T.useRef(null),{currentDiscussion:S}=e;T.useEffect(()=>(S&&S.status==="active"&&e.isDiscussionActive?(o(!0),D()):(o(!1),j.current&&(clearTimeout(j.current),j.current=null)),()=>{p.current&&clearInterval(p.current),j.current&&clearTimeout(j.current)}),[S,e.isDiscussionActive]),T.useEffect(()=>{var k;(k=u.current)==null||k.scrollIntoView({behavior:"smooth"})},[S==null?void 0:S.messages]),T.useEffect(()=>{if(S){const k=vp(S.messages,e.agents);let C;if(e.isDiscussionActive)C=Math.floor((Date.now()-new Date(S.createdAt).getTime())/1e3);else{const A=S.messages[S.messages.length-1];A?C=Math.floor((new Date(A.timestamp).getTime()-new Date(S.createdAt).getTime())/1e3):C=0}if(y({totalMessages:S.messages.length,consensusScore:k,activeTime:C}),e.isDiscussionActive&&(t({type:"UPDATE_CONSENSUS",payload:{consensusScore:k}}),k>80&&S.status==="active")){const A=jp(S.messages,S.topic);t({type:"UPDATE_CONSENSUS",payload:{consensusScore:k,consensus:A}}),o(!1),j.current&&(clearTimeout(j.current),j.current=null),setTimeout(async()=>{await U("达成共识")},2e3)}}},[S==null?void 0:S.messages]),T.useEffect(()=>{if(!e.isDiscussionActive&&e.currentDiscussion===null&&a===null){if(e.allDiscussions.length>0){const k=e.allDiscussions[0];k.status==="ended"&&c(k)}}else e.isDiscussionActive&&e.currentDiscussion&&(c(null),x(!1))},[e.isDiscussionActive,e.currentDiscussion,e.allDiscussions,a]);const D=()=>{if(!S||S.status!=="active")return;const k=S.participants,C=e.agents.filter(I=>k.includes(I.id)),A=S.moderatorId?e.agents.find(I=>I.id===S.moderatorId):null,P=async()=>{var H,Pt;if(!e.currentDiscussion||e.currentDiscussion.status!=="active"||!e.isDiscussionActive){o(!1);return}const I=e.currentDiscussion;if(A&&A.moderatorConfig){const be=A.moderatorConfig;if(I.messages.length-m>=be.summaryFrequency)try{const re=await Np(A,I,I.messages.slice(-be.summaryFrequency));w(re),d(I.messages.length);const nn=[...I.moderatorSummaries,re];t({type:"UPDATE_CONSENSUS",payload:{consensusScore:I.consensusScore,moderatorSummaries:nn}})}catch(re){console.error("主持人总结生成失败:",re)}if(I.messages.length>0)try{const re=await Sp(A,I,I.messages.slice(-3));if(N(re),re<be.interventionThreshold){const nn=(I.moderatorInterventions||0)+1;if(nn>=be.maxInterventions&&be.maxInterventions>0){console.log("达到最大干预次数，主持人终止讨论"),await U("偏离话题");return}const nd=await bp(A,I,"off_topic");if(r(nd,A.id,"statement"),t({type:"UPDATE_CONSENSUS",payload:{consensusScore:I.consensusScore,moderatorInterventions:nn}}),e.isDiscussionActive&&((H=e.currentDiscussion)==null?void 0:H.status)==="active"){const rd=Math.random()*3e3+2e3;j.current=setTimeout(P,rd)}return}}catch(re){console.error("话题相关性检查失败:",re)}try{const re=await Cp(A,I,be);if(re.shouldTerminate){console.log("主持人决定终止讨论:",re.reason),await U(re.reason||"主持人终止");return}}catch(re){console.error("讨论终止检查失败:",re)}}let pe;if(A&&I.mode==="moderator"?pe=await kp(A,I,C,I.messages):pe=ns(k,I.messages,I.mode),pe){const be=C.find(tn=>tn.id===pe);if(be)try{const tn=!!(A&&be.id===A.id),re=await yp(be,I,I.topic,I.messages.slice(-5),tn),nn=M(re);r(re,be.id,nn)}catch(tn){console.error("生成消息失败:",tn)}}if(e.isDiscussionActive&&((Pt=e.currentDiscussion)==null?void 0:Pt.status)==="active"){const be=Math.random()*3e3+2e3;j.current=setTimeout(P,be)}else o(!1)},K=Math.random()*2e3+1e3;j.current=setTimeout(P,K)},M=k=>k.includes("我赞同")||k.includes("我同意")||k.includes("这个想法很好")?"agreement":k.includes("但是")||k.includes("我认为")||k.includes("不同的看法")?"disagreement":k.includes("？")||k.includes("我们")||k.includes("如何")?"question":"statement",U=async k=>{if(!S)return;const C=S.moderatorSummaries.length>0?S.moderatorSummaries[S.moderatorSummaries.length-1]:v||"";c({...S,status:"ended",endReason:k,finalModeratorSummary:C,endedAt:new Date}),n(k)},z=async()=>{o(!1),j.current&&(clearTimeout(j.current),j.current=null),p.current&&(clearInterval(p.current),p.current=null),await U("手动终止")},ve=k=>{const C=Math.floor(k/60),A=k%60;return`${C}:${A.toString().padStart(2,"0")}`},It=k=>{switch(k){case"达成共识":return"bg-green-50 border-green-200 text-green-800";case"手动终止":return"bg-blue-50 border-blue-200 text-blue-800";case"偏离话题":return"bg-orange-50 border-orange-200 text-orange-800";case"主持人终止":return"bg-purple-50 border-purple-200 text-purple-800";case"时间超限":return"bg-yellow-50 border-yellow-200 text-yellow-800";case"消息数量达到上限":return"bg-indigo-50 border-indigo-200 text-indigo-800";default:return"bg-gray-50 border-gray-200 text-gray-800"}},_t=k=>{switch(k){case"达成共识":return s.jsx(et,{size:16,className:"text-green-600"});case"手动终止":return s.jsx(Ta,{size:16,className:"text-blue-600"});case"偏离话题":return s.jsx(Es,{size:16,className:"text-orange-600"});case"主持人终止":return s.jsx(ip,{size:16,className:"text-purple-600"});case"时间超限":return s.jsx(Ls,{size:16,className:"text-yellow-600"});case"消息数量达到上限":return s.jsx(sp,{size:16,className:"text-indigo-600"});default:return s.jsx(Ls,{size:16,className:"text-gray-600"})}},kr=k=>{switch(k){case"达成共识":return"参与者达成共识";case"手动终止":return"用户手动结束";case"偏离话题":return"讨论偏离主题";case"主持人终止":return"主持人主动终止";case"时间超限":return"讨论时间超限";case"消息数量达到上限":return"消息数量达到上限";default:return k||"未知原因"}},br=k=>{switch(k){case"达成共识":return"讨论参与者在话题上达成了足够的共识（共识度超过85%），系统自动结束了讨论";case"手动终止":return'用户主动点击"结束讨论"按钮终止了讨论';case"偏离话题":return"主持人检测到讨论内容偏离了原定话题，达到干预上限后终止讨论";case"主持人终止":return"主持人基于讨论情况（如活跃度、质量等）判断应该结束讨论";case"时间超限":return"讨论时间超过了预设的时间限制";case"消息数量达到上限":return"讨论消息数量达到了预设的上限";default:return"讨论因其他原因结束"}};if(!S){if(a){const k=e.agents.filter(C=>a.participants.includes(C.id));return s.jsx("div",{className:"h-full bg-gradient-to-br from-green-50 to-blue-50 w-full overflow-y-auto",children:s.jsxs("div",{className:"max-w-7xl mx-auto p-6 space-y-6",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center",children:[s.jsx(et,{size:64,className:"text-green-500 mx-auto mb-4"}),s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"讨论已结束"}),s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"讨论话题"}),s.jsx("p",{className:"text-2xl font-bold text-blue-900",children:a.topic})]}),a.endReason&&s.jsxs("div",{className:`border rounded-lg p-4 mb-6 ${It(a.endReason)}`,children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[_t(a.endReason),s.jsx("h4",{className:"text-sm font-semibold",children:"讨论结束原因"})]}),s.jsx("p",{className:"text-lg font-medium",children:kr(a.endReason)}),br(a.endReason)&&s.jsx("p",{className:"text-sm mt-2 opacity-80",children:br(a.endReason)}),s.jsxs("div",{className:"mt-3 pt-3 border-t border-current border-opacity-20",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm opacity-75",children:[s.jsxs("span",{children:["开始时间：",new Date(a.createdAt).toLocaleString()]}),a.endedAt&&s.jsxs("span",{children:["结束时间：",new Date(a.endedAt).toLocaleString()]})]}),a.endedAt&&s.jsx("div",{className:"text-center mt-2",children:s.jsxs("span",{className:"text-sm font-medium",children:["总时长：",Math.floor((new Date(a.endedAt).getTime()-new Date(a.createdAt).getTime())/6e4)," 分钟"]})})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[s.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:a.messages.length}),s.jsx("div",{className:"text-sm text-blue-800",children:"总消息数"})]}),s.jsxs("div",{className:"bg-green-50 rounded-lg p-4",children:[s.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[a.consensusScore,"%"]}),s.jsx("div",{className:"text-sm text-green-800",children:"共识度"})]}),s.jsxs("div",{className:"bg-purple-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:a.participants.length}),s.jsx("div",{className:"text-sm text-purple-800",children:"参与者"})]}),s.jsxs("div",{className:"bg-orange-50 rounded-lg p-4",children:[s.jsx("div",{className:"text-2xl font-bold text-orange-600",children:Math.floor((new Date().getTime()-new Date(a.createdAt).getTime())/6e4)}),s.jsx("div",{className:"text-sm text-orange-800",children:"讨论时长(分钟)"})]})]})]}),a.consensus&&s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(et,{size:32,className:"text-green-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"最终结论"})]}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:s.jsx("p",{className:"text-gray-800 text-lg leading-relaxed",children:a.consensus})})]}),a.moderatorId&&s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(rt,{size:32,className:"text-purple-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"主持人最后一次总结"}),s.jsx("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium",children:(()=>{const C=e.agents.find(A=>A.id===a.moderatorId);return C?C.name:"主持人"})()})]}),s.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),s.jsx("span",{className:"text-sm text-purple-700 font-medium",children:"讨论过程中的最后一次总结"})]}),a.finalModeratorSummary?s.jsx("p",{className:"text-gray-800 text-lg leading-relaxed whitespace-pre-wrap",children:a.finalModeratorSummary}):s.jsxs("div",{className:"text-center py-4",children:[s.jsx("p",{className:"text-gray-500 text-base",children:"主持人在讨论过程中未生成总结"}),s.jsx("p",{className:"text-gray-400 text-sm mt-2",children:"可能是因为讨论时间较短或消息数量未达到总结阈值"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(wl,{size:32,className:"text-indigo-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"消息类型分析"})]}),(()=>{const C={statement:{count:0,label:"陈述",color:"gray",icon:He},question:{count:0,label:"提问",color:"blue",icon:Xu},agreement:{count:0,label:"赞同",color:"green",icon:Ju},disagreement:{count:0,label:"反对",color:"red",icon:Zu}};a.messages.forEach(P=>{C[P.type]&&C[P.type].count++});const A=a.messages.length;return s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:Object.entries(C).map(([P,K])=>{const I=A>0?Math.round(K.count/A*100):0,pe=K.icon;return s.jsxs("div",{className:`bg-${K.color}-50 rounded-lg p-4 text-center`,children:[s.jsx(pe,{size:24,className:`text-${K.color}-600 mx-auto mb-2`}),s.jsx("div",{className:`text-2xl font-bold text-${K.color}-600`,children:K.count}),s.jsx("div",{className:`text-sm text-${K.color}-800 font-medium`,children:K.label}),s.jsxs("div",{className:`text-xs text-${K.color}-600 mt-1`,children:[I,"%"]})]},P)})})})()]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(rt,{size:32,className:"text-blue-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"参与者表现"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:k.map(C=>{const A=a.messages.filter(H=>H.agentId===C.id).length,P=a.messages.filter(H=>H.agentId===C.id&&H.type==="agreement").length,K=a.messages.filter(H=>H.agentId===C.id&&H.type==="disagreement").length,I=a.messages.filter(H=>H.agentId===C.id&&H.type==="question").length,pe=a.messages.filter(H=>H.agentId===C.id&&H.type==="statement").length;return s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx("img",{src:C.avatar,alt:C.name,className:"w-12 h-12 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-semibold text-gray-900",children:C.name}),s.jsx("p",{className:"text-sm text-gray-500",children:C.expertise.slice(0,2).join("、")})]})]}),s.jsxs("div",{className:"space-y-2 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"总发言"}),s.jsx("span",{className:"font-medium",children:A})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"陈述"}),s.jsx("span",{className:"font-medium text-gray-600",children:pe})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"提问"}),s.jsx("span",{className:"font-medium text-blue-600",children:I})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"赞同"}),s.jsx("span",{className:"font-medium text-green-600",children:P})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"反对"}),s.jsx("span",{className:"font-medium text-red-600",children:K})]})]})]},C.id)})})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(He,{size:32,className:"text-purple-600"}),s.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"完整消息记录"}),s.jsxs("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium",children:[a.messages.length," 条消息"]})]}),s.jsxs("button",{onClick:()=>x(!f),className:"flex items-center gap-2 px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors",children:[f?"收起消息":"展开消息",s.jsx(wl,{size:16})]})]}),f?s.jsx("div",{className:"max-h-96 overflow-y-auto space-y-4 border border-gray-200 rounded-lg p-4",children:a.messages.map(C=>{const A=k.find(P=>P.id===C.agentId);return s.jsx(Ia,{message:C,agent:A},C.id)})}):s.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 text-center",children:[s.jsx("p",{className:"text-gray-500 mb-4",children:'点击"展开消息"查看完整的讨论记录'}),s.jsxs("div",{className:"flex justify-center gap-4 text-sm text-gray-400",children:[s.jsxs("span",{children:["首条消息：",new Date((en=a.messages[0])==null?void 0:en.timestamp).toLocaleString()]}),s.jsx("span",{children:"•"}),s.jsxs("span",{children:["末条消息：",new Date((E=a.messages[a.messages.length-1])==null?void 0:E.timestamp).toLocaleString()]})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsxs("div",{className:"flex gap-4 justify-center",children:[s.jsx("button",{onClick:()=>{l(a),c(null),x(!1)},className:"px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"回到讨论室"}),s.jsx("button",{onClick:()=>{},className:"px-8 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium",children:"查看历史"}),s.jsx("button",{onClick:()=>{const C={topic:a.topic,participants:k.map(I=>I.name),messages:a.messages.map(I=>{var pe;return{speaker:((pe=k.find(H=>H.id===I.agentId))==null?void 0:pe.name)||"Unknown",content:I.content,type:I.type,timestamp:new Date(I.timestamp).toLocaleString()}}),consensus:a.consensus,consensusScore:a.consensusScore,duration:Math.floor((new Date().getTime()-new Date(a.createdAt).getTime())/6e4)},A=new Blob([JSON.stringify(C,null,2)],{type:"application/json"}),P=URL.createObjectURL(A),K=document.createElement("a");K.href=P,K.download=`讨论记录_${a.topic}_${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(K),K.click(),document.body.removeChild(K),URL.revokeObjectURL(P)},className:"px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium",children:"导出记录"})]}),s.jsx("div",{className:"mt-6 text-center",children:s.jsx("p",{className:"text-sm text-gray-500",children:'您可以通过导航栏的"创建讨论"按钮开始新的讨论'})})]})]})})}return s.jsx("div",{className:"h-full bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-y-auto",children:s.jsx("div",{className:"discussion-container",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-2xl",children:[s.jsx(He,{size:64,className:"text-gray-400 mx-auto mb-4"}),s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有进行中的讨论"}),s.jsx("p",{className:"text-gray-600",children:"请先创建一个新的讨论来开始智能体对话。"})]})})})}const zt=e.agents.filter(k=>S.participants.includes(k.id));return s.jsx("div",{className:"h-screen bg-gradient-to-br from-blue-50 to-indigo-50 w-full overflow-hidden pt-16",children:s.jsx("div",{className:"discussion-container h-full",children:s.jsxs("div",{className:"flex gap-6 h-full max-w-none w-full",children:[S.moderatorId&&(()=>{const k=e.agents.find(C=>C.id===S.moderatorId);return k?s.jsx("div",{className:"fixed-size-sidebar flex-shrink-0",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6 h-fit",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:s.jsx(rt,{size:16,className:"text-purple-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"主持人"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:k.avatar,alt:k.name,className:"w-12 h-12 rounded-full object-cover"}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900",children:k.name}),s.jsx("p",{className:"text-sm text-gray-500",children:k.expertise.slice(0,2).join("、")})]})]}),k.moderatorConfig&&s.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-3",children:[s.jsx("h5",{className:"text-sm font-medium text-purple-900 mb-2",children:"管理风格"}),s.jsx("p",{className:"text-sm text-purple-700",children:k.moderatorConfig.managementStyle==="strict"?"严格型":k.moderatorConfig.managementStyle==="flexible"?"灵活型":"协作型"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"话题相关性"}),s.jsxs("span",{className:`font-medium ${h>.8?"text-green-600":h>.6?"text-yellow-600":"text-red-600"}`,children:[Math.round(h*100),"%"]})]}),s.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full overflow-hidden",children:s.jsx("div",{className:`h-full transition-all duration-500 ${h>.8?"bg-green-500":h>.6?"bg-yellow-500":"bg-red-500"}`,style:{width:`${h*100}%`}})})]}),v&&s.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[s.jsx("h5",{className:"text-sm font-medium text-blue-900 mb-2",children:"最新总结"}),s.jsx("p",{className:"text-sm text-blue-700",children:v})]}),s.jsxs("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"干预次数"}),s.jsx("span",{className:"font-medium text-gray-900",children:S.moderatorInterventions||0})]})]})]})}):null})(),s.jsx("div",{className:"fixed-size-discussion flex-shrink-0 h-[70vh]",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden h-full flex flex-col",children:[s.jsxs("div",{className:`text-white p-6 ${e.isDiscussionActive?"bg-gradient-to-r from-blue-600 to-indigo-600":"bg-gradient-to-r from-gray-600 to-gray-700"}`,children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(He,{size:32}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold",children:e.isDiscussionActive?"讨论进行中":"历史讨论记录"}),s.jsx("p",{className:e.isDiscussionActive?"text-blue-100":"text-gray-100",children:S.mode==="free"?"自由讨论模式":"主持人模式"})]})]}),s.jsx("div",{className:"flex items-center gap-4",children:e.isDiscussionActive?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold",children:ve(g.activeTime)}),s.jsx("div",{className:"text-sm text-blue-100",children:"讨论时长"})]}),s.jsxs("button",{onClick:z,className:"flex items-center gap-2 bg-red-500 hover:bg-red-600 px-4 py-2 rounded-lg transition-colors",children:[s.jsx(Ta,{size:20}),"结束讨论"]})]}):s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-sm text-gray-100",children:"创建时间"}),s.jsx("div",{className:"text-lg font-bold",children:new Date(S.createdAt).toLocaleString()})]})})]}),s.jsxs("div",{className:"bg-white bg-opacity-20 rounded-lg p-4",children:[s.jsx("h3",{className:"font-semibold mb-2",children:"讨论话题"}),s.jsx("p",{className:e.isDiscussionActive?"text-blue-50":"text-gray-50",children:S.topic})]})]}),s.jsxs("div",{className:"h-[calc(100%-180px)] overflow-y-auto p-6 space-y-4",children:[S.messages.map(k=>s.jsx(Ia,{message:k,agent:zt.find(C=>C.id===k.agentId)},k.id)),i&&e.isDiscussionActive&&s.jsxs("div",{className:"flex items-center gap-3 text-gray-500",children:[s.jsx("div",{className:"animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"}),s.jsx("span",{children:"智能体正在思考中..."})]}),s.jsx("div",{ref:u})]})]})}),s.jsxs("div",{className:"space-y-6 fixed-size-sidebar",children:[s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(ed,{size:24,className:"text-green-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"共识度"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"w-full h-4 bg-gray-200 rounded-full overflow-hidden",children:s.jsx("div",{className:`h-full transition-all duration-500 ${g.consensusScore>80?"bg-green-500":g.consensusScore>60?"bg-yellow-500":"bg-red-500"}`,style:{width:`${g.consensusScore}%`}})}),s.jsxs("div",{className:"text-center mt-2 font-bold text-2xl",children:[Math.round(g.consensusScore),"%"]})]}),S.status==="consensus"&&s.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-800 p-3 rounded-lg",children:[s.jsx(et,{size:20}),s.jsx("span",{className:"font-medium",children:"已达成共识！"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(rt,{size:24,className:"text-blue-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"参与者"})]}),s.jsx("div",{className:"space-y-3",children:zt.filter(k=>k.id!==S.moderatorId).map(k=>{const C=S.messages.filter(P=>P.agentId===k.id).length,A=S.messages.slice().reverse().find(P=>P.agentId===k.id);return s.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[s.jsx("img",{src:k.avatar,alt:k.name,className:"w-10 h-10 rounded-full object-cover"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"font-medium text-gray-900",children:k.name}),s.jsxs("div",{className:"text-sm text-gray-500",children:[C," 条消息"]})]}),A&&s.jsx("div",{className:"text-xs text-gray-400",children:new Date(A.timestamp).toLocaleTimeString()})]},k.id)})})]}),s.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(wl,{size:24,className:"text-purple-600"}),s.jsx("h3",{className:"font-semibold text-gray-900",children:"讨论统计"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"总消息数"}),s.jsx("span",{className:"font-bold text-lg",children:g.totalMessages})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"讨论时长"}),s.jsx("span",{className:"font-bold text-lg",children:ve(g.activeTime)})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"参与者数量"}),s.jsx("span",{className:"font-bold text-lg",children:zt.length})]})]})]}),S.consensus&&s.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[s.jsx(et,{size:24,className:"text-green-600"}),s.jsx("h3",{className:"font-semibold text-green-900",children:"讨论结论"})]}),s.jsx("p",{className:"text-green-800",children:S.consensus})]})]})]})})})}function Ia({message:e,agent:t}){const n=()=>{switch(e.type){case"question":return s.jsx(Xu,{size:16,className:"text-blue-500"});case"agreement":return s.jsx(Ju,{size:16,className:"text-green-500"});case"disagreement":return s.jsx(Zu,{size:16,className:"text-red-500"});default:return s.jsx(He,{size:16,className:"text-gray-500"})}},r=()=>{switch(e.type){case"question":return"border-l-blue-500";case"agreement":return"border-l-green-500";case"disagreement":return"border-l-red-500";default:return"border-l-gray-300"}};return s.jsxs("div",{className:`flex gap-4 p-4 bg-gray-50 rounded-lg border-l-4 ${r()}`,children:[s.jsx("img",{src:t==null?void 0:t.avatar,alt:t==null?void 0:t.name,className:"w-12 h-12 rounded-full object-cover flex-shrink-0"}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("span",{className:"font-semibold text-gray-900",children:t==null?void 0:t.name}),n(),s.jsx("span",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleTimeString()})]}),s.jsx("p",{className:"text-gray-800 leading-relaxed",children:e.content})]})]})}const Lp=({isOpen:e,onClose:t,onSave:n,editingConfig:r})=>{const[l,i]=T.useState({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),[o,a]=T.useState(!1),[c,f]=T.useState(!1),[x,g]=T.useState(null),[y,v]=T.useState([]),[w,h]=T.useState("");T.useEffect(()=>{r?(i(r),h("")):(i({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),h("")),g(null),v([])},[r,e]);const N=p=>{if(h(p),p){const j=Ma.find(S=>S.id===p);j&&i(S=>({...S,name:j.name,provider:j.provider.toLowerCase(),model:j.model,temperature:j.defaultSettings.temperature,maxTokens:j.defaultSettings.maxTokens}))}},m=(p,j)=>{if(i(S=>({...S,[p]:j})),g(null),y.length>0){const S=O.validateLLMConfig({...l,[p]:j});v(S)}},d=async()=>{const p=O.validateLLMConfig(l);if(p.length>0){v(p);return}f(!0),g(null);try{const j={id:"test",name:l.name,provider:l.provider,model:l.model,apiKey:l.apiKey,baseURL:l.baseURL,temperature:l.temperature,maxTokens:l.maxTokens,systemPrompt:l.systemPrompt},S=await Jt.testLLMConfig(j);g({success:S,message:S?"连接测试成功！":"连接测试失败，请检查配置。"})}catch(j){g({success:!1,message:`连接测试失败: ${j instanceof Error?j.message:"未知错误"}`})}finally{f(!1)}},u=()=>{const p=O.validateLLMConfig(l);if(p.length>0){v(p);return}const j={id:(r==null?void 0:r.id)||O.generateLLMConfigId(),name:l.name,provider:l.provider,model:l.model,apiKey:l.apiKey,baseURL:l.baseURL,temperature:l.temperature,maxTokens:l.maxTokens,systemPrompt:l.systemPrompt};n(j),t()};return e?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx("h2",{className:"text-xl font-bold",children:r?"编辑LLM配置":"新建LLM配置"}),s.jsx("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:s.jsx(cp,{className:"w-6 h-6"})})]}),!r&&s.jsxs("div",{className:"mb-6",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择预设配置（可选）"}),s.jsxs("select",{value:w,onChange:p=>N(p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"",children:"自定义配置"}),Ma.map(p=>s.jsxs("option",{value:p.id,children:[p.name," - ",p.description]},p.id))]})]}),y.length>0&&s.jsxs("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx(Dn,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("span",{className:"text-red-700 font-medium",children:"配置错误"})]}),s.jsx("ul",{className:"mt-2 text-sm text-red-600",children:y.map((p,j)=>s.jsxs("li",{children:["• ",p]},j))})]}),x&&s.jsx("div",{className:`mb-4 p-3 border rounded-md ${x.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:s.jsxs("div",{className:"flex items-center",children:[x.success?s.jsx(et,{className:"w-5 h-5 text-green-500 mr-2"}):s.jsx(Dn,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("span",{className:`font-medium ${x.success?"text-green-700":"text-red-700"}`,children:x.message})]})}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"配置名称 *"}),s.jsx("input",{type:"text",value:l.name||"",onChange:p=>m("name",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：我的GPT-4配置"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"提供商 *"}),s.jsxs("select",{value:l.provider||"openai",onChange:p=>m("provider",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[s.jsx("option",{value:"openai",children:"OpenAI"}),s.jsx("option",{value:"anthropic",children:"Anthropic"}),s.jsx("option",{value:"azure",children:"Azure OpenAI"}),s.jsx("option",{value:"custom",children:"自定义"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"模型名称 *"}),s.jsx("input",{type:"text",value:l.model||"",onChange:p=>m("model",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：gpt-4, claude-3-opus-20240229"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"API密钥 *"}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:o?"text":"password",value:l.apiKey||"",onChange:p=>m("apiKey",p.target.value),className:"w-full p-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入API密钥"}),s.jsx("button",{type:"button",onClick:()=>a(!o),className:"absolute right-2 top-2 text-gray-500 hover:text-gray-700",children:o?s.jsx(qf,{className:"w-5 h-5"}):s.jsx(Xf,{className:"w-5 h-5"})})]})]}),(l.provider==="azure"||l.provider==="custom")&&s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["基础URL ",l.provider==="azure"?"*":"(可选)"]}),s.jsx("input",{type:"text",value:l.baseURL||"",onChange:p=>m("baseURL",p.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:l.provider==="azure"?"https://your-resource.openai.azure.com":"https://api.example.com"})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"温度 (0-2)"}),s.jsx("input",{type:"number",min:"0",max:"2",step:"0.1",value:l.temperature||.7,onChange:p=>m("temperature",parseFloat(p.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最大令牌数"}),s.jsx("input",{type:"number",min:"1",max:"4000",value:l.maxTokens||1e3,onChange:p=>m("maxTokens",parseInt(p.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"自定义系统提示词 (可选)"}),s.jsx("textarea",{value:l.systemPrompt||"",onChange:p=>m("systemPrompt",p.target.value),rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"添加额外的系统提示词..."})]})]}),s.jsxs("div",{className:"flex justify-between mt-6",children:[s.jsxs("button",{onClick:d,disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[s.jsx(lp,{className:"w-4 h-4 mr-2"}),c?"测试中...":"测试连接"]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsx("button",{onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"取消"}),s.jsxs("button",{onClick:u,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[s.jsx(np,{className:"w-4 h-4 mr-2"}),"保存"]})]})]})]})}):null},Tp=()=>{const[e,t]=T.useState([]),[n,r]=T.useState(!1),[l,i]=T.useState(null),[o,a]=T.useState(!1),[c,f]=T.useState(!0),[x,g]=T.useState({total:0,byProvider:{},recentlyUsed:[]});T.useEffect(()=>{y()},[]);const y=async()=>{try{f(!0);const[u,p]=await Promise.all([O.getLLMConfigs(),O.getLLMConfigStats()]);t(u),g(p)}catch(u){console.error("Failed to load configs:",u),t([]),g({total:0,byProvider:{},recentlyUsed:[]})}finally{f(!1)}},v=async u=>{try{await O.saveLLMConfig(u),await y()}catch{alert("保存配置失败")}},w=u=>{i(u),r(!0)},h=async u=>{if(confirm("确定要删除这个LLM配置吗？"))try{await O.deleteLLMConfig(u),await y()}catch{alert("删除配置失败")}},N=()=>{i(null),r(!0)},m=async()=>{try{const u=await O.exportLLMConfigs(),p=new Blob([u],{type:"application/json"}),j=URL.createObjectURL(p),S=document.createElement("a");S.href=j,S.download=`llm-configs-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(S),S.click(),document.body.removeChild(S),URL.revokeObjectURL(j)}catch{alert("导出失败")}},d=u=>{var S;const p=(S=u.target.files)==null?void 0:S[0];if(!p)return;const j=new FileReader;j.onload=async D=>{var M;try{const U=(M=D.target)==null?void 0:M.result,z=await O.importLLMConfigs(U);z.success>0&&(alert(`成功导入 ${z.success} 个配置`),await y()),z.errors.length>0&&alert(`导入时遇到错误：
${z.errors.join(`
`)}`)}catch{alert("导入失败：文件格式错误")}},j.readAsText(p),u.target.value=""};return c?s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsx("div",{className:"centered-content",children:s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-600",children:"正在加载LLM配置..."})]})})})})}):s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsxs("div",{className:"centered-content",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsxs("div",{children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[s.jsx(bt,{className:"w-8 h-8 mr-3 text-blue-600"}),"LLM配置管理"]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"管理大语言模型配置，为智能体提供AI能力"})]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsxs("button",{onClick:()=>a(!o),className:"flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:[s.jsx(Ct,{className:"w-4 h-4 mr-2"}),"导入/导出"]}),s.jsxs("button",{onClick:N,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[s.jsx(yi,{className:"w-4 h-4 mr-2"}),"新建配置"]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:x.total}),s.jsx("div",{className:"text-sm text-gray-600",children:"总配置数"})]}),s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:Object.keys(x.byProvider).length}),s.jsx("div",{className:"text-sm text-gray-600",children:"支持的提供商"})]}),s.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:x.recentlyUsed.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"最近使用"})]})]}),o&&s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200",children:[s.jsx("h3",{className:"text-lg font-medium mb-3",children:"导入/导出配置"}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsxs("button",{onClick:m,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[s.jsx(xi,{className:"w-4 h-4 mr-2"}),"导出配置"]}),s.jsxs("label",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[s.jsx(vi,{className:"w-4 h-4 mr-2"}),"导入配置",s.jsx("input",{type:"file",accept:".json",onChange:d,className:"hidden"})]})]}),s.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"导出的配置文件会隐藏API密钥，导入时需要重新设置"})]}),s.jsx("div",{className:"bg-white rounded-lg border border-gray-200",children:e.length===0?s.jsxs("div",{className:"p-8 text-center",children:[s.jsx(bt,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有LLM配置"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"创建第一个LLM配置来为智能体提供AI能力"}),s.jsxs("button",{onClick:N,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mx-auto",children:[s.jsx(yi,{className:"w-4 h-4 mr-2"}),"新建配置"]})]}):s.jsx("div",{className:"divide-y divide-gray-200",children:e.map(u=>s.jsxs("div",{className:"p-4 hover:bg-gray-50",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:"text-2xl",children:Ds(u.provider)}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-medium text-gray-900",children:u.name}),s.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[s.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${Ms(u.provider)}`,children:u.provider.toUpperCase()}),s.jsx("span",{className:"text-sm text-gray-600",children:u.model})]})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs("div",{className:"text-right text-sm text-gray-600",children:[s.jsxs("div",{children:["温度: ",u.temperature]}),s.jsxs("div",{children:["令牌: ",u.maxTokens]})]}),s.jsxs("div",{className:"flex space-x-1",children:[s.jsx("button",{onClick:()=>w(u),className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md",title:"编辑配置",children:s.jsx(Yu,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>h(u.id),className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md",title:"删除配置",children:s.jsx(yr,{className:"w-4 h-4"})})]})]})]}),u.systemPrompt&&s.jsxs("div",{className:"mt-3 p-3 bg-gray-50 rounded-md",children:[s.jsx("div",{className:"text-sm text-gray-600",children:s.jsx("strong",{children:"自定义系统提示词:"})}),s.jsx("div",{className:"text-sm text-gray-800 mt-1 line-clamp-2",children:u.systemPrompt})]})]},u.id))})}),Object.keys(x.byProvider).length>0&&s.jsxs("div",{className:"mt-6 bg-white p-4 rounded-lg border border-gray-200",children:[s.jsx("h3",{className:"text-lg font-medium mb-3",children:"提供商分布"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:Object.entries(x.byProvider).map(([u,p])=>s.jsxs("div",{className:`flex items-center px-3 py-1 rounded-full text-sm ${Ms(u)}`,children:[s.jsx("span",{className:"mr-1",children:Ds(u)}),u.toUpperCase(),": ",p]},u))})]}),s.jsx(Lp,{isOpen:n,onClose:()=>{r(!1),i(null)},onSave:v,editingConfig:l})]})})})},Dp=()=>{const{state:e,exportData:t,importData:n,clearAllData:r}=At(),[l,i]=T.useState(!1),[o,a]=T.useState(!1),[c,f]=T.useState(null),[x,g]=T.useState(!1),[y,v]=T.useState(null);T.useEffect(()=>{(async()=>{try{const u=await O.getStorageInfo();v(u)}catch(u){console.error("Failed to fetch storage info:",u),v({used:0,available:0,total:0})}})()},[]);const w=d=>{if(d===0)return"0 Bytes";const u=1024,p=["Bytes","KB","MB","GB"],j=Math.floor(Math.log(d)/Math.log(u));return parseFloat((d/Math.pow(u,j)).toFixed(2))+" "+p[j]},h=async()=>{i(!0);try{const d=await t(),u=new Blob([d],{type:"application/json"}),p=URL.createObjectURL(u),j=document.createElement("a");j.href=p,j.download=`multi-agent-system-backup-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(j),j.click(),document.body.removeChild(j),URL.revokeObjectURL(p)}catch(d){f({success:!1,message:"导出失败: "+(d instanceof Error?d.message:"未知错误")})}finally{i(!1)}},N=async d=>{var p;const u=(p=d.target.files)==null?void 0:p[0];if(u){a(!0),f(null);try{const j=await u.text(),S=await n(j);f({success:S,message:S?"数据导入成功！":"数据导入失败，请检查文件格式。"})}catch(j){f({success:!1,message:"导入失败: "+(j instanceof Error?j.message:"未知错误")})}finally{a(!1),d.target.value=""}}},m=async()=>{try{await r(),g(!1),f({success:!0,message:"所有数据已清除，系统已重置为默认状态。"})}catch(d){f({success:!1,message:"清除数据失败: "+(d instanceof Error?d.message:"未知错误")})}};return s.jsx("div",{className:"h-full overflow-y-auto",children:s.jsx("div",{className:"centered-container",children:s.jsxs("div",{className:"centered-content",children:[s.jsx("div",{className:"flex justify-between items-center mb-6",children:s.jsxs("div",{children:[s.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[s.jsx(go,{className:"w-8 h-8 mr-3 text-blue-600"}),"数据管理"]}),s.jsx("p",{className:"text-gray-600 mt-1",children:"管理系统数据的备份、恢复和清理"})]})}),c&&s.jsx("div",{className:`mb-6 p-4 rounded-lg border ${c.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:s.jsxs("div",{className:"flex items-center",children:[c.success?s.jsx(et,{className:"w-5 h-5 text-green-500 mr-2"}):s.jsx(Es,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("span",{className:`font-medium ${c.success?"text-green-700":"text-red-700"}`,children:c.message})]})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e.agents.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"智能体"})]}),s.jsx(Ct,{className:"w-8 h-8 text-blue-500"})]})}),s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:e.allDiscussions.length}),s.jsx("div",{className:"text-sm text-gray-600",children:"历史讨论"})]}),s.jsx(Yf,{className:"w-8 h-8 text-green-500"})]})}),s.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:y?w(y.used):"加载中..."}),s.jsx("div",{className:"text-sm text-gray-600",children:"已用存储"})]}),s.jsx(Zf,{className:"w-8 h-8 text-purple-500"})]})})]}),s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200 mb-8",children:[s.jsx("h3",{className:"text-lg font-medium mb-4",children:"存储使用情况"}),y?s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"已使用"}),s.jsx("span",{children:w(y.used)})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all",style:{width:`${y.used/y.total*100}%`}})}),s.jsxs("div",{className:"flex justify-between text-sm text-gray-600",children:[s.jsxs("span",{children:["可用: ",w(y.available)]}),s.jsxs("span",{children:["总计: ",w(y.total)]})]})]}):s.jsx("div",{className:"text-center text-gray-500",children:"加载存储信息中..."})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[s.jsxs("div",{className:"flex items-center mb-4",children:[s.jsx(xi,{className:"w-6 h-6 text-green-600 mr-3"}),s.jsx("h3",{className:"text-lg font-medium",children:"导出数据"})]}),s.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"将所有配置和历史记录导出为JSON文件，用于备份或迁移。"}),s.jsxs("button",{onClick:h,disabled:l,className:"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[s.jsx(xi,{className:"w-4 h-4 mr-2"}),l?"导出中...":"导出数据"]})]}),s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[s.jsxs("div",{className:"flex items-center mb-4",children:[s.jsx(vi,{className:"w-6 h-6 text-blue-600 mr-3"}),s.jsx("h3",{className:"text-lg font-medium",children:"导入数据"})]}),s.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"从备份文件恢复配置和历史记录。将覆盖当前数据。"}),s.jsxs("label",{className:"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[s.jsx(vi,{className:"w-4 h-4 mr-2"}),o?"导入中...":"选择文件",s.jsx("input",{type:"file",accept:".json",onChange:N,disabled:o,className:"hidden"})]})]}),s.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[s.jsxs("div",{className:"flex items-center mb-4",children:[s.jsx(yr,{className:"w-6 h-6 text-red-600 mr-3"}),s.jsx("h3",{className:"text-lg font-medium",children:"清除数据"})]}),s.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"清除所有数据并重置为默认状态。此操作不可撤销。"}),x?s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:[s.jsx(Es,{className:"w-4 h-4 mr-2"}),"确定要清除所有数据吗？"]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:m,className:"flex-1 px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"确认清除"}),s.jsx("button",{onClick:()=>g(!1),className:"flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400",children:"取消"})]})]}):s.jsxs("button",{onClick:()=>g(!0),className:"w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:[s.jsx(yr,{className:"w-4 h-4 mr-2"}),"清除所有数据"]})]})]}),s.jsx("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start",children:[s.jsx(Jf,{className:"w-5 h-5 text-blue-500 mr-2 mt-0.5"}),s.jsxs("div",{className:"text-sm text-blue-700",children:[s.jsx("div",{className:"font-medium mb-1",children:"使用说明："}),s.jsxs("ul",{className:"space-y-1 text-xs",children:[s.jsx("li",{children:"• 导出的数据包含智能体配置、LLM配置、讨论历史等所有信息"}),s.jsx("li",{children:"• 导入数据会覆盖当前所有配置，建议先导出备份"}),s.jsx("li",{children:"• 清除数据会删除所有自定义配置，但会保留默认智能体"}),s.jsx("li",{children:"• 数据存储在浏览器本地，清除浏览器数据会丢失所有配置"})]})]})]})})]})})})},Mp=({onNavigate:e})=>{const{state:t,dispatch:n,setCurrentDiscussion:r}=At(),[l,i]=T.useState(""),[o,a]=T.useState("date"),[c,f]=T.useState("all"),[x,g]=T.useState(null),[y,v]=T.useState(null),w=async u=>{if(confirm("确定要删除这条讨论记录吗？此操作不可撤销。"))try{v(u),await O.deleteDiscussion(u);const p=await O.getDiscussions();n({type:"SET_ALL_DISCUSSIONS",payload:p})}catch(p){console.error("删除讨论失败:",p),alert("删除失败，请重试")}finally{v(null)}},h=u=>{r(u),e("discussion")},N=t.allDiscussions.filter(u=>{const p=u.topic.toLowerCase().includes(l.toLowerCase()),j=c==="all"||u.status===c;return p&&j}).sort((u,p)=>{switch(o){case"date":return new Date(p.createdAt).getTime()-new Date(u.createdAt).getTime();case"topic":return u.topic.localeCompare(p.topic);case"messages":return p.messages.length-u.messages.length;default:return 0}}),m=u=>{switch(u){case"consensus":return s.jsx(et,{className:"w-4 h-4 text-green-500"});case"ended":return s.jsx(Ls,{className:"w-4 h-4 text-gray-500"});default:return s.jsx(He,{className:"w-4 h-4 text-blue-500"})}},d=u=>{switch(u){case"consensus":return"已达成共识";case"ended":return"已结束";default:return"进行中"}};return s.jsx("div",{className:"h-[calc(100vh-4rem)] bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-hidden",children:s.jsx("div",{className:"h-full p-6",children:s.jsxs("div",{className:"bg-white rounded-xl shadow-lg h-full flex flex-col",children:[s.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-t-xl",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx(Ts,{size:32}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold",children:"讨论历史"}),s.jsx("p",{className:"text-purple-100",children:"查看和管理历史讨论记录"})]})]}),s.jsxs("div",{className:"flex gap-4 items-center",children:[s.jsxs("div",{className:"flex-1 relative",children:[s.jsx(rp,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"text",placeholder:"搜索讨论话题...",value:l,onChange:u=>i(u.target.value),className:"w-full pl-10 pr-4 py-2 rounded-lg text-gray-900 placeholder-gray-500"})]}),s.jsxs("select",{value:o,onChange:u=>a(u.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[s.jsx("option",{value:"date",children:"按时间排序"}),s.jsx("option",{value:"topic",children:"按话题排序"}),s.jsx("option",{value:"messages",children:"按消息数排序"})]}),s.jsxs("select",{value:c,onChange:u=>f(u.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[s.jsx("option",{value:"all",children:"全部状态"}),s.jsx("option",{value:"consensus",children:"已达成共识"}),s.jsx("option",{value:"ended",children:"已结束"})]})]})]}),s.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:N.length===0?s.jsxs("div",{className:"text-center py-12",children:[s.jsx(Ts,{size:64,className:"text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"暂无讨论记录"}),s.jsx("p",{className:"text-gray-600",children:"开始一个新的讨论来创建历史记录"})]}):s.jsx("div",{className:"space-y-4",children:N.map(u=>{const p=t.agents.filter(j=>u.participants.includes(j.id));return s.jsx("div",{className:"bg-gray-50 rounded-lg border border-gray-200",children:s.jsxs("div",{className:"p-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[m(u.status),s.jsx("h3",{className:"font-semibold text-gray-900",children:u.topic}),s.jsx("span",{className:"text-sm text-gray-500",children:d(u.status)})]}),s.jsxs("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(Wf,{className:"w-4 h-4"}),new Date(u.createdAt).toLocaleString()]}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(He,{className:"w-4 h-4"}),u.messages.length," 条消息"]}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(rt,{className:"w-4 h-4"}),p.length," 位参与者"]}),u.consensusScore!==void 0&&s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(ed,{className:"w-4 h-4"}),"共识度 ",Math.round(u.consensusScore),"%"]})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("button",{onClick:()=>h(u),className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors",title:"转到聊天室",children:s.jsx(Gf,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>w(u.id),disabled:y===u.id,className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"删除记录",children:y===u.id?s.jsx("div",{className:"animate-spin w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full"}):s.jsx(yr,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>g(x===u.id?null:u.id),className:"p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors",title:"查看详情",children:x===u.id?s.jsx(Kf,{className:"w-4 h-4"}):s.jsx(Qf,{className:"w-4 h-4"})})]})]}),x===u.id&&s.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"参与者"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:p.map(j=>s.jsxs("div",{className:"flex items-center gap-2 bg-white px-3 py-1 rounded-full border",children:[s.jsx("img",{src:j.avatar,alt:j.name,className:"w-6 h-6 rounded-full object-cover"}),s.jsx("span",{className:"text-sm font-medium",children:j.name})]},j.id))})]}),u.consensus&&s.jsxs("div",{className:"mb-4",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"讨论结论"}),s.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:s.jsx("p",{className:"text-green-800",children:u.consensus})})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"消息预览"}),s.jsxs("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[u.messages.slice(0,5).map(j=>{const S=p.find(D=>D.id===j.agentId);return s.jsxs("div",{className:"bg-white p-3 rounded border",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx("span",{className:"font-medium text-sm",children:S==null?void 0:S.name}),s.jsx("span",{className:"text-xs text-gray-500",children:new Date(j.timestamp).toLocaleTimeString()})]}),s.jsx("p",{className:"text-sm text-gray-700 line-clamp-2",children:j.content})]},j.id)}),u.messages.length>5&&s.jsxs("div",{className:"text-center text-sm text-gray-500",children:["还有 ",u.messages.length-5," 条消息..."]})]})]})]})]})},u.id)})})})]})})})},Ap=()=>{const{state:e}=At(),[t,n]=T.useState(!1),[r,l]=T.useState([{id:"storage",label:"初始化存储服务",status:"pending"},{id:"server",label:"检查服务器连接",status:"pending"},{id:"agents",label:"加载智能体配置",status:"pending"},{id:"llm",label:"加载LLM配置",status:"pending"},{id:"discussions",label:"加载讨论历史",status:"pending"}]);T.useEffect(()=>{l(c=>c.map(f=>{const x=c.findIndex(y=>y.id===f.id),g=c.findIndex(y=>y.id===e.loadingStep);return x<g?{...f,status:"completed"}:x===g?{...f,status:"loading"}:{...f,status:"pending"}}))},[e.loadingStep]),T.useEffect(()=>{const c=setTimeout(()=>{n(!0)},1e4);return()=>clearTimeout(c)},[]);const i=c=>{switch(c){case"completed":return s.jsx(et,{className:"w-4 h-4 text-green-500"});case"loading":return s.jsx(Ea,{className:"w-4 h-4 text-blue-500 animate-spin"});case"error":return s.jsx(Dn,{className:"w-4 h-4 text-red-500"});default:return s.jsx(Ls,{className:"w-4 h-4 text-gray-400"})}},o=r.filter(c=>c.status==="completed").length,a=o/r.length*100;return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center max-w-md mx-auto",children:[s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-8",children:[s.jsx(bt,{size:48,className:"text-blue-600 animate-pulse"}),s.jsx("h1",{className:"text-4xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[s.jsx(Ea,{className:"w-6 h-6 text-blue-600 animate-spin"}),s.jsx("span",{className:"text-lg text-gray-600",children:"正在初始化系统..."})]}),s.jsxs("div",{className:"w-80 mx-auto mb-6",children:[s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-500",style:{width:`${a}%`}})}),s.jsxs("div",{className:"mt-2 text-sm text-gray-500",children:[o,"/",r.length," 步骤完成"]})]}),s.jsx("div",{className:"space-y-3 text-sm",children:r.map(c=>s.jsxs("div",{className:"flex items-center justify-center gap-3",children:[i(c.status),s.jsx("span",{className:`${c.status==="completed"?"text-green-600":c.status==="loading"?"text-blue-600":c.status==="error"?"text-red-600":"text-gray-500"}`,children:c.label})]},c.id))}),t&&s.jsxs("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[s.jsxs("div",{className:"flex items-center gap-2 text-yellow-800",children:[s.jsx(Dn,{className:"w-5 h-5"}),s.jsx("span",{className:"font-medium",children:"加载时间较长"})]}),s.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"连接服务器超时，原因是网络较慢或后台服务不可用。"})]})]})})};class Ip extends T.Component{constructor(n){super(n);Ae(this,"handleReload",()=>{window.location.reload()});Ae(this,"handleReset",()=>{this.setState({hasError:!1,error:null,errorInfo:null})});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(n){return{hasError:!0,error:n,errorInfo:null}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){var n,r,l;if(this.state.hasError){const i=(r=(n=this.state.error)==null?void 0:n.message)==null?void 0:r.includes("无法连接到后端服务器");return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center max-w-2xl mx-auto p-8",children:[s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[s.jsx(Es,{size:48,className:"text-red-500"}),s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:i?"无法连接后端服务":"系统初始化失败"})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"错误详情"}),s.jsx("div",{className:"text-left bg-gray-50 rounded p-4 mb-4",children:s.jsx("p",{className:"text-red-600 font-mono text-sm",children:((l=this.state.error)==null?void 0:l.message)||"未知错误"})}),s.jsx("div",{className:"text-sm text-gray-600 mb-4",children:i?s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"font-medium mb-2",children:"请检查以下项目："}),s.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[s.jsx("li",{children:"后端服务是否已启动（端口5000）"}),s.jsx("li",{children:"网络连接是否正常"}),s.jsx("li",{children:"防火墙是否阻止了连接"}),s.jsx("li",{children:"后端服务地址配置是否正确"})]})]}):s.jsxs(s.Fragment,{children:[s.jsx("p",{children:"可能的原因："}),s.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[s.jsx("li",{children:"网络连接问题"}),s.jsx("li",{children:"后端服务器不可用"}),s.jsx("li",{children:"浏览器存储空间不足"}),s.jsx("li",{children:"配置文件损坏"})]})]})})]}),s.jsxs("div",{className:"flex gap-4 justify-center",children:[s.jsxs("button",{onClick:this.handleReload,className:"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[s.jsx(tp,{size:20}),"重新加载页面"]}),s.jsx("button",{onClick:this.handleReset,className:"flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"重试初始化"})]}),i&&s.jsxs("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[s.jsx("p",{className:"text-blue-800 font-medium mb-2",children:"启动后端服务："}),s.jsxs("div",{className:"text-sm text-blue-700 space-y-1",children:[s.jsxs("p",{children:["Windows: 运行 ",s.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"start.bat"})]}),s.jsxs("p",{children:["Linux/macOS: 运行 ",s.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"./start.sh"})]})]})]})]})})}return this.props.children}}function _p(){const{state:e}=At(),[t,n]=T.useState("home");if(Ba.useEffect(()=>{e.isDiscussionActive&&e.currentDiscussion&&n("discussion")},[e.isDiscussionActive,e.currentDiscussion]),e.isLoading)return s.jsx(Ap,{});const r=()=>{switch(t){case"agents":return s.jsx(pp,{});case"llm":return s.jsx(Tp,{});case"data":return s.jsx(Dp,{});case"history":return s.jsx(Mp,{onNavigate:n});case"setup":return s.jsx(xp,{});case"discussion":return s.jsx(Ep,{});default:return s.jsx(zp,{onNavigate:n})}};return s.jsxs("div",{className:"h-screen bg-gray-50 w-full flex flex-col",children:[t!=="home"&&s.jsx("nav",{className:"flex-shrink-0 bg-white shadow-sm border-b border-gray-200 w-full",children:s.jsx("div",{className:"w-full px-6",children:s.jsxs("div",{className:"flex items-center justify-between h-16",children:[s.jsxs("div",{className:"flex items-center gap-8",children:[s.jsxs("button",{onClick:()=>n("home"),className:"flex items-center gap-2 text-gray-900 hover:text-blue-600 font-medium",children:[s.jsx(bt,{size:24,className:"text-blue-600"}),"多智能体讨论系统"]}),s.jsxs("div",{className:"flex gap-6",children:[s.jsxs("button",{onClick:()=>n("agents"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="agents"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(rt,{size:20}),"智能体管理"]}),s.jsxs("button",{onClick:()=>n("llm"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="llm"?"bg-orange-100 text-orange-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(qu,{size:20}),"LLM管理"]}),s.jsxs("button",{onClick:()=>n("history"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="history"?"bg-purple-100 text-purple-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(Ts,{size:20}),"讨论历史"]}),s.jsxs("button",{onClick:()=>n("data"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="data"?"bg-indigo-100 text-indigo-700":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(go,{size:20}),"数据管理"]}),s.jsxs("button",{onClick:()=>n("setup"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="setup"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,disabled:e.agents.length===0,children:[s.jsx(Ct,{size:20}),"创建讨论"]}),e.isDiscussionActive&&s.jsxs("button",{onClick:()=>n("discussion"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="discussion"?"bg-green-100 text-green-700":"text-green-600 hover:text-green-700"}`,children:[s.jsx(He,{size:20}),"讨论进行中",s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]})]})]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsxs("span",{className:"text-sm text-gray-500",children:[e.agents.length," 个智能体"]}),e.isDiscussionActive&&s.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"讨论中"]})]})]})})}),s.jsx("div",{className:"flex-1 overflow-hidden",children:r()})]})}function zp({onNavigate:e}){var n;const{state:t}=At();return s.jsx("div",{className:"h-full bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 w-full fixed-layout overflow-y-auto",children:s.jsxs("div",{className:"w-full px-6 py-12 fixed-layout",children:[s.jsxs("div",{className:"text-center mb-16",children:[s.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[s.jsx(bt,{size:48,className:"text-blue-600"}),s.jsx("h1",{className:"text-5xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),s.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"通过配置不同专业背景和思维方式的AI智能体，创建富有洞察力的讨论环境， 探索复杂问题的多维度解决方案，并达成有价值的共识。"}),s.jsxs("div",{className:"flex items-center justify-center gap-8 mt-8 text-sm text-gray-500",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Da,{size:16,className:"text-yellow-500"}),"支持2-8个智能体同时讨论"]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(bt,{size:16,className:"text-purple-500"}),"智能共识判断算法"]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(He,{size:16,className:"text-blue-500"}),"实时讨论模拟"]})]})]}),s.jsxs("div",{className:"space-y-8 mb-16",children:[s.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-blue-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors",children:s.jsx(rt,{size:32,className:"text-blue-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"智能体管理"}),s.jsx("p",{className:"text-gray-500",children:"配置AI智能体"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"创建和配置具有不同专业背景、思维方式和性格特征的智能体。 每个智能体都有独特的知识领域和讨论风格。"}),s.jsxs("div",{className:"mb-6",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[s.jsx("span",{children:"当前智能体数量"}),s.jsxs("span",{className:"font-bold text-blue-600 text-lg",children:[t.agents.length,"/8"]})]}),s.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:s.jsx("div",{className:"h-full bg-blue-500 rounded-full transition-all",style:{width:`${t.agents.length/8*100}%`}})})]}),s.jsx("button",{onClick:()=>e("agents"),className:"w-full bg-blue-600 text-white py-3 rounded-xl hover:bg-blue-700 transition-colors font-medium",children:"管理智能体"})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-orange-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center group-hover:bg-orange-200 transition-colors",children:s.jsx(qu,{size:32,className:"text-orange-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"LLM管理"}),s.jsx("p",{className:"text-gray-500",children:"配置大语言模型"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"配置和管理大语言模型，为智能体提供真实的AI对话能力。 支持OpenAI、Anthropic等多种提供商。"}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"支持多种LLM提供商"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"个性化配置参数"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"连接测试功能"]})]}),s.jsx("button",{onClick:()=>e("llm"),className:"w-full bg-orange-600 text-white py-3 rounded-xl hover:bg-orange-700 transition-colors font-medium",children:"管理LLM配置"})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-indigo-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-indigo-100 rounded-xl flex items-center justify-center group-hover:bg-indigo-200 transition-colors",children:s.jsx(go,{size:32,className:"text-indigo-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"数据管理"}),s.jsx("p",{className:"text-gray-500",children:"备份与恢复"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"管理系统数据的备份、恢复和清理，确保配置和历史记录的安全。"}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"数据导出备份"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"配置导入恢复"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),"数据清理重置"]})]}),s.jsx("button",{onClick:()=>e("data"),className:"w-full bg-indigo-600 text-white py-3 rounded-xl hover:bg-indigo-700 transition-colors font-medium",children:"管理数据"})]})})]}),s.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:s.jsx(Ct,{size:32,className:"text-purple-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"创建讨论"}),s.jsx("p",{className:"text-gray-500",children:"配置讨论参数"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"设置讨论话题、选择参与的智能体、配置讨论模式， 开始一场富有见解的AI讨论。"}),s.jsxs("div",{className:"space-y-3 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"自由讨论模式"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"主持人模式"]}),s.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[s.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),"智能共识判断"]})]}),s.jsx("button",{onClick:()=>e("setup"),disabled:t.agents.length<2,className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium",children:t.agents.length<2?"需要至少2个智能体":"创建新讨论"})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-green-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors",children:s.jsx(He,{size:32,className:"text-green-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论状态"}),s.jsx("p",{className:"text-gray-500",children:"实时监控"})]})]}),t.isDiscussionActive?s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"当前有一场讨论正在进行中，您可以实时观看智能体之间的对话， 监控共识度变化。"}),s.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),s.jsx("span",{className:"font-medium text-green-800",children:"讨论进行中"})]}),s.jsxs("p",{className:"text-green-700 text-sm",children:["话题：",(n=t.currentDiscussion)==null?void 0:n.topic]})]}),s.jsx("button",{onClick:()=>e("discussion"),className:"w-full bg-green-600 text-white py-3 rounded-xl hover:bg-green-700 transition-colors font-medium",children:"进入讨论室"})]}):s.jsxs(s.Fragment,{children:[s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"目前没有进行中的讨论。创建智能体并配置讨论参数后， 您就可以开始一场精彩的AI对话。"}),s.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),s.jsx("span",{className:"font-medium text-gray-600",children:"空闲状态"})]}),s.jsxs("p",{className:"text-gray-500 text-sm",children:["历史讨论：",t.allDiscussions.length," 场"]})]}),s.jsx("button",{onClick:()=>e(t.agents.length<2?"agents":"setup"),className:"w-full bg-gray-400 text-white py-3 rounded-xl hover:bg-gray-500 transition-colors font-medium",children:t.agents.length<2?"先创建智能体":"开始新讨论"})]})]})}),s.jsx("div",{className:"group",children:s.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[s.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:s.jsx(Ts,{size:32,className:"text-purple-600"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论历史"}),s.jsx("p",{className:"text-gray-500",children:"查看历史记录"})]})]}),s.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"查看和分析历史讨论记录，了解智能体的对话模式和共识形成过程。"}),s.jsxs("div",{className:"mb-6",children:[s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[s.jsx("span",{children:"历史讨论数量"}),s.jsx("span",{className:"font-bold text-purple-600 text-lg",children:t.allDiscussions.length})]}),s.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:s.jsx("div",{className:"h-full bg-purple-500 rounded-full transition-all",style:{width:`${Math.min(t.allDiscussions.length/10*100,100)}%`}})})]}),s.jsx("button",{onClick:()=>e("history"),className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 transition-colors font-medium",children:"查看历史"})]})})]})]}),s.jsxs("div",{className:"bg-white rounded-2xl shadow-xl p-12 border border-gray-100",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 text-center mb-8",children:"系统特色功能"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(bt,{size:32,className:"text-blue-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"智能化对话"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"基于专业领域和性格特征生成真实的对话内容"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(Da,{size:32,className:"text-green-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"实时共识监控"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"动态计算讨论共识度，智能判断达成一致的时机"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(rt,{size:32,className:"text-purple-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"多模式讨论"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"支持自由讨论和主持人模式，适应不同讨论需求"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:s.jsx(He,{size:32,className:"text-orange-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"丰富的交互"}),s.jsx("p",{className:"text-gray-600 text-sm",children:"支持多种消息类型，包括陈述、提问、同意、反对"})]})]})]})]})})}function Pp(){return s.jsx(Ip,{children:s.jsx(Ff,{children:s.jsx(_p,{})})})}Nl.createRoot(document.getElementById("root")).render(s.jsx(Ba.StrictMode,{children:s.jsx(Pp,{})}));
