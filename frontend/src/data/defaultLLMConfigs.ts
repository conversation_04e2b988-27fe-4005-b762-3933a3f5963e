import { PresetLLMConfig } from '../types';

// 预设的LLM配置模板
export const defaultLLMConfigs: PresetLLMConfig[] = [
  {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'OpenAI',
    model: 'gpt-4-turbo-preview',
    description: 'OpenAI最新的GPT-4 Turbo模型，性能强大，适合复杂推理',
    defaultSettings: {
      temperature: 0.7,
      maxTokens: 1000
    }
  },
  {
    id: 'gpt-4',
    name: 'GPT-4',
    provider: 'OpenAI',
    model: 'gpt-4',
    description: 'OpenAI的旗舰模型，适合需要高质量输出的场景',
    defaultSettings: {
      temperature: 0.7,
      maxTokens: 1000
    }
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'OpenAI',
    model: 'gpt-3.5-turbo',
    description: '快速且经济的模型，适合大多数对话任务',
    defaultSettings: {
      temperature: 0.7,
      maxTokens: 800
    }
  },
  {
    id: 'claude-3-opus',
    name: 'Claude 3 Opus',
    provider: 'Anthropic',
    model: 'claude-3-opus-20240229',
    description: 'Anthropic最强大的模型，擅长复杂分析和创作',
    defaultSettings: {
      temperature: 0.7,
      maxTokens: 1000
    }
  },
  {
    id: 'claude-3-sonnet',
    name: 'Claude 3 Sonnet',
    provider: 'Anthropic',
    model: 'claude-3-sonnet-20240229',
    description: '平衡性能和成本的优秀选择',
    defaultSettings: {
      temperature: 0.7,
      maxTokens: 800
    }
  },
  {
    id: 'claude-3-haiku',
    name: 'Claude 3 Haiku',
    provider: 'Anthropic',
    model: 'claude-3-haiku-20240307',
    description: '快速响应的轻量级模型，适合简单任务',
    defaultSettings: {
      temperature: 0.7,
      maxTokens: 600
    }
  },
  {
    id: 'gpt-4-azure',
    name: 'Azure GPT-4',
    provider: 'Azure',
    model: 'gpt-4',
    description: '部署在Azure上的GPT-4模型',
    defaultSettings: {
      temperature: 0.7,
      maxTokens: 1000
    }
  },
  {
    id: 'gpt-35-turbo-azure',
    name: 'Azure GPT-3.5 Turbo',
    provider: 'Azure',
    model: 'gpt-35-turbo',
    description: '部署在Azure上的GPT-3.5 Turbo模型',
    defaultSettings: {
      temperature: 0.7,
      maxTokens: 800
    }
  }
];

// 根据智能体特征推荐LLM配置
export function recommendLLMConfig(
  expertise: string[],
  thinkingStyle: string,
  personality: string
): PresetLLMConfig[] {
  const recommendations: PresetLLMConfig[] = [];

  // 基于思维方式推荐
  if (thinkingStyle === 'creative') {
    recommendations.push(
      defaultLLMConfigs.find(c => c.id === 'claude-3-opus')!,
      defaultLLMConfigs.find(c => c.id === 'gpt-4')!
    );
  } else if (thinkingStyle === 'analytical' || thinkingStyle === 'logical') {
    recommendations.push(
      defaultLLMConfigs.find(c => c.id === 'gpt-4')!,
      defaultLLMConfigs.find(c => c.id === 'claude-3-sonnet')!
    );
  } else if (thinkingStyle === 'intuitive') {
    recommendations.push(
      defaultLLMConfigs.find(c => c.id === 'claude-3-opus')!,
      defaultLLMConfigs.find(c => c.id === 'gpt-4-turbo')!
    );
  } else {
    recommendations.push(
      defaultLLMConfigs.find(c => c.id === 'gpt-3.5-turbo')!,
      defaultLLMConfigs.find(c => c.id === 'claude-3-haiku')!
    );
  }

  // 基于专业领域推荐
  const hasComplexDomain = expertise.some(exp => 
    ['法律', '医疗', '数据分析', '技术'].includes(exp)
  );
  
  if (hasComplexDomain && !recommendations.some(r => r.id === 'gpt-4')) {
    recommendations.unshift(defaultLLMConfigs.find(c => c.id === 'gpt-4')!);
  }

  // 基于性格特征调整
  if (personality === 'diplomatic' || personality === 'thoughtful') {
    const claudeConfig = defaultLLMConfigs.find(c => c.id === 'claude-3-sonnet');
    if (claudeConfig && !recommendations.includes(claudeConfig)) {
      recommendations.unshift(claudeConfig);
    }
  }

  return recommendations.filter(Boolean).slice(0, 3);
}

// 获取模型的成本等级
export function getModelCostLevel(modelId: string): 'low' | 'medium' | 'high' {
  const highCostModels = ['gpt-4', 'gpt-4-turbo-preview', 'claude-3-opus-20240229'];
  const mediumCostModels = ['claude-3-sonnet-20240229', 'gpt-4-azure'];
  
  if (highCostModels.includes(modelId)) return 'high';
  if (mediumCostModels.includes(modelId)) return 'medium';
  return 'low';
}

// 获取模型的速度等级
export function getModelSpeedLevel(modelId: string): 'fast' | 'medium' | 'slow' {
  const fastModels = ['gpt-3.5-turbo', 'claude-3-haiku-20240307', 'gpt-35-turbo'];
  const slowModels = ['gpt-4', 'claude-3-opus-20240229'];
  
  if (fastModels.includes(modelId)) return 'fast';
  if (slowModels.includes(modelId)) return 'slow';
  return 'medium';
}

// 获取模型的能力描述
export function getModelCapabilities(modelId: string): string[] {
  const capabilities: Record<string, string[]> = {
    'gpt-4': ['复杂推理', '代码生成', '创意写作', '多语言支持'],
    'gpt-4-turbo-preview': ['最新知识', '长文本处理', '复杂推理', '多模态'],
    'gpt-3.5-turbo': ['快速响应', '对话优化', '成本效益', '通用任务'],
    'claude-3-opus-20240229': ['深度分析', '创意写作', '复杂推理', '安全可靠'],
    'claude-3-sonnet-20240229': ['平衡性能', '准确回答', '适中成本', '可靠输出'],
    'claude-3-haiku-20240307': ['极速响应', '简洁回答', '低成本', '高效处理']
  };
  
  return capabilities[modelId] || ['通用对话', '文本生成'];
}
