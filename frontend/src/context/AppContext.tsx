import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Agent, Discussion, Message, DiscussionConfig } from '../types';
import { storageService, AppSettings, UserPreferences } from '../services/storageService';
import { apiService } from '../services/apiService';
import { v4 as uuidv4 } from 'uuid';

interface AppState {
  agents: Agent[];
  currentDiscussion: Discussion | null;
  allDiscussions: Discussion[];
  isDiscussionActive: boolean;
  settings: AppSettings | null;
  preferences: UserPreferences | null;
  isLoading: boolean;
  loadingStep: string;
}

type AppAction =
  | { type: 'ADD_AGENT'; payload: Agent }
  | { type: 'UPDATE_AGENT'; payload: Agent }
  | { type: 'DELETE_AGENT'; payload: string }
  | { type: 'START_DISCUSSION'; payload: DiscussionConfig }
  | { type: 'SET_CURRENT_DISCUSSION'; payload: Discussion }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'END_DISCUSSION' }
  | { type: 'UPDATE_CONSENSUS'; payload: {
      consensusScore: number;
      consensus?: string;
      moderatorInterventions?: number;
      topicRelevanceScore?: number;
      moderatorSummaries?: string[];
    } }
  | { type: 'LOAD_STATE'; payload: AppState }
  | { type: 'UPDATE_SETTINGS'; payload: AppSettings }
  | { type: 'UPDATE_PREFERENCES'; payload: UserPreferences }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_LOADING_STEP'; payload: string }
  | { type: 'SET_ALL_DISCUSSIONS'; payload: Discussion[] }
  | { type: 'INITIALIZE_SUCCESS'; payload: { agents: Agent[]; discussions: Discussion[]; settings: AppSettings; preferences: UserPreferences } };

const initialState: AppState = {
  agents: [],
  currentDiscussion: null,
  allDiscussions: [],
  isDiscussionActive: false,
  settings: null,
  preferences: null,
  isLoading: true,
  loadingStep: 'storage',
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  addAgent: (config: Omit<Agent, 'id' | 'isActive'>) => void;
  updateAgent: (agent: Agent) => void;
  deleteAgent: (id: string) => void;
  startDiscussion: (config: DiscussionConfig) => void;
  setCurrentDiscussion: (discussion: Discussion) => void;
  endDiscussion: (reason?: string) => void;
  sendMessage: (content: string, agentId: string, type: Message['type']) => void;
  updateSettings: (settings: AppSettings) => void;
  updatePreferences: (preferences: UserPreferences) => void;
  exportData: () => Promise<string>;
  importData: (data: string) => Promise<boolean>;
  clearAllData: () => Promise<void>;
}>({
  state: initialState,
  dispatch: () => null,
  addAgent: () => null,
  updateAgent: () => null,
  deleteAgent: () => null,
  startDiscussion: () => null,
  setCurrentDiscussion: () => null,
  endDiscussion: () => null,
  sendMessage: () => null,
  updateSettings: () => null,
  updatePreferences: () => null,
  exportData: async () => '',
  importData: async () => false,
  clearAllData: async () => {},
});

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'ADD_AGENT':
      return {
        ...state,
        agents: [...state.agents, action.payload],
      };
    
    case 'UPDATE_AGENT':
      return {
        ...state,
        agents: state.agents.map(agent => 
          agent.id === action.payload.id ? action.payload : agent
        ),
      };
    
    case 'DELETE_AGENT':
      return {
        ...state,
        agents: state.agents.filter(agent => agent.id !== action.payload),
      };
    
    case 'START_DISCUSSION':
      const newDiscussion: Discussion = {
        id: uuidv4(),
        topic: action.payload.topic,
        mode: action.payload.mode,
        participants: action.payload.selectedAgents,
        messages: [],
        status: 'active',
        consensus: null,
        createdAt: new Date(),
        consensusScore: 0,
        moderatorId: action.payload.moderatorId,
        moderatorSummaries: [],
        topicRelevanceScore: 1.0,
        moderatorInterventions: 0,
      };
      return {
        ...state,
        currentDiscussion: newDiscussion,
        isDiscussionActive: true,
      };

    case 'SET_CURRENT_DISCUSSION':
      return {
        ...state,
        currentDiscussion: action.payload,
        isDiscussionActive: false, // 历史讨论不是活跃状态
      };
    
    case 'ADD_MESSAGE':
      if (!state.currentDiscussion) return state;
      
      const updatedDiscussion = {
        ...state.currentDiscussion,
        messages: [...state.currentDiscussion.messages, action.payload],
      };
      
      return {
        ...state,
        currentDiscussion: updatedDiscussion,
      };
    
    case 'UPDATE_CONSENSUS':
      if (!state.currentDiscussion) return state;

      const updatedDiscussionWithConsensus = {
        ...state.currentDiscussion,
        consensusScore: action.payload.consensusScore,
        consensus: action.payload.consensus || state.currentDiscussion.consensus,
        status: action.payload.consensusScore > 80 ? 'consensus' as const : state.currentDiscussion.status,
        moderatorInterventions: action.payload.moderatorInterventions ?? state.currentDiscussion.moderatorInterventions,
        topicRelevanceScore: action.payload.topicRelevanceScore ?? state.currentDiscussion.topicRelevanceScore,
        moderatorSummaries: action.payload.moderatorSummaries ?? state.currentDiscussion.moderatorSummaries,
      };
      
      return {
        ...state,
        currentDiscussion: updatedDiscussionWithConsensus,
      };
    
    case 'END_DISCUSSION':
      if (!state.currentDiscussion) return state;
      
      const endedDiscussion = {
        ...state.currentDiscussion,
        status: 'ended' as const,
      };
      
      return {
        ...state,
        currentDiscussion: null,
        allDiscussions: [endedDiscussion, ...state.allDiscussions],
        isDiscussionActive: false,
      };
    
    case 'LOAD_STATE':
      return action.payload;
    
    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: action.payload,
      };
    
    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: action.payload,
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case 'SET_LOADING_STEP':
      return {
        ...state,
        loadingStep: action.payload,
      };
    
    case 'SET_ALL_DISCUSSIONS':
      return {
        ...state,
        allDiscussions: action.payload,
      };
    
    case 'INITIALIZE_SUCCESS':
      return {
        ...state,
        agents: action.payload.agents,
        allDiscussions: action.payload.discussions,
        settings: action.payload.settings,
        preferences: action.payload.preferences,
        isLoading: false,
        loadingStep: 'complete',
      };
    
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // 初始化存储服务和加载数据
  useEffect(() => {
    let isCancelled = false; // 防止组件卸载后继续执行

    const initializeApp = async () => {
      if (isCancelled) return;
      const startTime = Date.now();
      try {
        console.log('Starting app initialization...');
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_LOADING_STEP', payload: 'storage' });

        // 初始化存储服务
        console.log('Initializing storage service...');
        const storageStartTime = Date.now();
        await storageService.initialize();
        if (isCancelled) return; // 检查是否已取消

        console.log(`Storage service initialized in ${Date.now() - storageStartTime}ms`);
        dispatch({ type: 'SET_LOADING_STEP', payload: 'server' });

        // 检查服务器连接状态
        const isServerAvailable = storageService.isServerAvailable();
        if (!isServerAvailable) {
          console.warn('后端服务器不可用，系统无法正常工作');
        }
        if (isCancelled) return; // 检查是否已取消
        dispatch({ type: 'SET_LOADING_STEP', payload: 'agents' });

        // 加载数据
        console.log('Loading data...');
        const dataStartTime = Date.now();
        const loadedData = await loadAllData();
        dispatch({ type: 'SET_LOADING_STEP', payload: 'discussions' });

        console.log(`Data loaded in ${Date.now() - dataStartTime}ms`);
        
        dispatch({
          type: 'INITIALIZE_SUCCESS',
          payload: loadedData
        });

        console.log(`App initialization completed in ${Date.now() - startTime}ms`);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        console.error('Error details:', {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          initTime: Date.now() - startTime
        });

        // 直接抛出错误让ErrorBoundary处理
        throw error;
      }
    };

    initializeApp();

    // 清理函数，防止组件卸载后继续执行
    return () => {
      isCancelled = true;
    };
  }, []);

  // 自动保存状态变化（防抖处理）
  useEffect(() => {
    if (!state.isLoading) {
      const timeoutId = setTimeout(async () => {
        try {
          console.log('Auto-saving data...');

          // 并行保存所有数据
          const savePromises = [];

          // 保存智能体
          savePromises.push(storageService.saveAgents(state.agents));

          // 保存讨论记录
          const allDiscussions = state.currentDiscussion
            ? [...state.allDiscussions, state.currentDiscussion]
            : state.allDiscussions;
          savePromises.push(storageService.saveDiscussions(allDiscussions));

          // 保存设置和偏好
          if (state.settings) {
            savePromises.push(storageService.saveSettings(state.settings));
          }
          if (state.preferences) {
            savePromises.push(storageService.savePreferences(state.preferences));
          }

          await Promise.all(savePromises);
          console.log('Auto-save completed');
        } catch (error) {
          console.error('Failed to auto-save data:', error);
        }
      }, 1000); // 1秒防抖

      return () => clearTimeout(timeoutId);
    }
  }, [state.agents, state.allDiscussions, state.currentDiscussion, state.settings, state.preferences, state.isLoading]);

  const addAgent = (config: Omit<Agent, 'id' | 'isActive'>) => {
    const newAgent: Agent = {
      ...config,
      id: uuidv4(),
      isActive: true,
    };
    dispatch({ type: 'ADD_AGENT', payload: newAgent });
  };

  const updateAgent = (agent: Agent) => {
    dispatch({ type: 'UPDATE_AGENT', payload: agent });
  };

  const deleteAgent = async (id: string) => {
    try {
      // 先从服务器删除
      await storageService.deleteAgent(id);
      // 然后更新本地状态
      dispatch({ type: 'DELETE_AGENT', payload: id });
    } catch (error) {
      console.error('Failed to delete agent:', error);
      throw error;
    }
  };

  const startDiscussion = (config: DiscussionConfig) => {
    dispatch({ type: 'START_DISCUSSION', payload: config });
  };

  const setCurrentDiscussion = (discussion: Discussion) => {
    dispatch({ type: 'SET_CURRENT_DISCUSSION', payload: discussion });
  };

  const endDiscussion = async (reason?: string) => {
    if (state.currentDiscussion) {
      try {
        // 先标记为已结束
        const endedDiscussion = {
          ...state.currentDiscussion,
          status: 'ended' as const,
          endReason: reason || '手动终止',
        };

        // 保存到服务器
        await storageService.saveDiscussion(endedDiscussion);

        // 更新本地状态
        dispatch({ type: 'END_DISCUSSION' });

        // 重新加载所有讨论
        const allDiscussions = await storageService.getDiscussions();
        dispatch({ type: 'SET_ALL_DISCUSSIONS', payload: allDiscussions });
      } catch (error) {
        console.error('Failed to save discussion:', error);
        // 即使保存失败也要结束讨论
        dispatch({ type: 'END_DISCUSSION' });
      }
    } else {
      dispatch({ type: 'END_DISCUSSION' });
    }
  };

  const sendMessage = async (content: string, agentId: string, type: Message['type']) => {
    const message: Message = {
      id: uuidv4(),
      agentId,
      content,
      type,
      timestamp: new Date(),
    };
    
    // 先更新本地状态
    dispatch({ type: 'ADD_MESSAGE', payload: message });
    
    // 保存消息到服务器
    if (state.currentDiscussion) {
      try {
        await apiService.addMessage(state.currentDiscussion.id, message);
      } catch (error) {
        console.error('Failed to save message:', error);
      }
    }
  };

  const updateSettings = (settings: AppSettings) => {
    dispatch({ type: 'UPDATE_SETTINGS', payload: settings });
  };

  const updatePreferences = (preferences: UserPreferences) => {
    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });
  };

  // 统一的数据加载方法
  const loadAllData = async () => {
    try {
      const [agents, discussions, settings, preferences] = await Promise.all([
        storageService.getAgents(),
        storageService.getDiscussions(),
        storageService.getSettings(),
        storageService.getPreferences()
      ]);

      return {
        agents: agents || [],
        discussions: discussions || [],
        settings: settings || {} as AppSettings,
        preferences: preferences || {} as UserPreferences
      };
    } catch (error) {
      console.error('Failed to load data:', error);
      throw error;
    }
  };

  const exportData = async (): Promise<string> => {
    try {
      const data = await storageService.getAllData();
      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('Failed to export data:', error);
      throw new Error('导出数据失败');
    }
  };

  const importData = async (jsonData: string): Promise<boolean> => {
    try {
      const data = JSON.parse(jsonData);
      
      if (!storageService.validateData(data)) {
        throw new Error('数据格式无效');
      }
      
      await storageService.importAllData(data);

      // 重新加载数据到状态
      const loadedData = await loadAllData();
      dispatch({
        type: 'INITIALIZE_SUCCESS',
        payload: loadedData
      });
      
      return true;
    } catch (error) {
      console.error('Failed to import data:', error);
      return false;
    }
  };

  const clearAllData = async () => {
    try {
      await storageService.clearAllData();
      dispatch({
        type: 'INITIALIZE_SUCCESS',
        payload: {
          agents: [],
          discussions: [],
          settings: {} as AppSettings,
          preferences: {} as UserPreferences
        }
      });
    } catch (error) {
      console.error('Failed to clear data:', error);
      throw new Error('清除数据失败');
    }
  };

  return (
    <AppContext.Provider
      value={{
        state,
        dispatch,
        addAgent,
        updateAgent,
        deleteAgent,
        startDiscussion,
        setCurrentDiscussion,
        endDiscussion,
        sendMessage,
        updateSettings,
        updatePreferences,
        exportData,
        importData,
        clearAllData,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};
