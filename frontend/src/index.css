@tailwind base;
@tailwind components;
@tailwind utilities;

/* 重置默认样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8fafc;
  color: #1e293b;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 固定尺寸容器 */
.container-fixed {
  width: 100%;
  max-width: none;
  overflow-x: auto;
  min-width: fit-content;
}

/* 页面容器样式 */
.page-container {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 固定布局容器 */
.fixed-layout {
  min-width: fit-content;
  overflow-x: auto;
}

/* 固定尺寸的组件样式 */
.fixed-size-card {
  width: 350px;
  min-width: 350px;
  max-width: 350px;
  flex-shrink: 0;
}

.fixed-size-discussion {
  width: 900px;
  min-width: 900px;
  max-width: 900px;
  flex-shrink: 0;
}

.fixed-size-sidebar {
  width: 320px;
  min-width: 320px;
  max-width: 320px;
  flex-shrink: 0;
}

/* 聊天室居中布局 */
.discussion-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100%;
  padding: 24px;
  gap: 24px;
}

.discussion-content {
  display: flex;
  gap: 24px;
  max-width: 1620px; /* 320px + 900px + 320px + gaps */
  width: 100%;
}

/* 通用居中容器 */
.centered-container {
  display: flex;
  justify-content: center;
  min-height: 100%;
  padding: 24px;
}

.centered-content {
  width: 100%;
  max-width: 1400px; /* 适合大多数内容的最大宽度 */
}
