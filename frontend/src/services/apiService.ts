/**
 * API服务 - 与后端服务器通信
 */

import { Agent, LLMConfig, Discussion, Message } from '../types';
import { AppSettings, UserPreferences, StorageData } from './storageService';

// API基础URL
const API_BASE_URL = import.meta.env.VITE_API_URL;

// HTTP请求工具类
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    // 设置默认超时时间
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      signal: controller.signal,
      ...options,
    };

    try {
      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      console.error(`API request failed: ${url}`, error);
      throw error;
    }
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// API服务类
export class ApiService {
  private client: ApiClient;
  private isOnline: boolean = true;
  private connectionPromise: Promise<boolean> | null = null;

  constructor() {
    this.client = new ApiClient(API_BASE_URL);
    // 不在构造函数中调用异步方法
  }

  // 检查服务器连接（带去重机制）
  async checkConnection(): Promise<boolean> {
    // 如果已有正在进行的连接检查，返回同一个Promise
    if (this.connectionPromise) {
      console.log('[ApiService] Connection check already in progress, reusing promise');
      return this.connectionPromise;
    }

    // 创建新的连接检查Promise
    this.connectionPromise = this._performConnectionCheck();

    try {
      const result = await this.connectionPromise;
      return result;
    } finally {
      // 清除Promise引用，允许下次重新检查
      this.connectionPromise = null;
    }
  }

  private async _performConnectionCheck(): Promise<boolean> {
    try {
      // 如果没有配置 API_BASE_URL，直接返回 false
      if (!API_BASE_URL) {
        console.warn('[ApiService] VITE_API_URL not configured');
        this.isOnline = false;
        return false;
      }

      const baseUrl = API_BASE_URL.replace('/api', '');
      const healthUrl = `${baseUrl}/health`;

      console.log(`[ApiService] Checking connection to: ${healthUrl}`);

      // 创建 AbortController 和超时处理
      const controller = new AbortController();
      let timeoutId: number | null = null;

      try {
        // 设置超时
        timeoutId = setTimeout(() => {
          console.log('[ApiService] Connection check timeout, aborting request');
          controller.abort();
        }, 8000); // 增加到8秒超时

        const response = await fetch(healthUrl, {
          signal: controller.signal,
          method: 'GET',
          cache: 'no-cache',
          headers: {
            'Cache-Control': 'no-cache'
          }
        });

        // 清除超时
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        console.log(`[ApiService] Response status: ${response.status}`);

        if (response.ok) {
          this.isOnline = true;
          console.log('[ApiService] Connection check successful');
          return true;
        } else {
          this.isOnline = false;
          console.warn(`[ApiService] Connection check failed with status: ${response.status}`);
          return false;
        }
      } catch (fetchError) {
        // 清除超时
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        // 重新抛出错误以便外层catch处理
        throw fetchError;
      }
    } catch (error) {
      console.warn('[ApiService] Backend server is not available:', error);

      // 提供更详细的错误信息
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.warn('[ApiService] Request was aborted (likely due to timeout)');
        } else if (error.name === 'TypeError' && error.message.includes('fetch')) {
          console.warn('[ApiService] Network error or server not reachable');
        } else {
          console.warn(`[ApiService] Unexpected error: ${error.name} - ${error.message}`);
        }
      }

      this.isOnline = false;
      return false;
    }
  }

  // 获取连接状态
  getConnectionStatus(): boolean {
    return this.isOnline;
  }

  // ============= LLM配置相关 =============

  async getLLMConfigs(): Promise<LLMConfig[]> {
    return this.client.get<LLMConfig[]>('/llm-configs');
  }

  async createLLMConfig(config: Omit<LLMConfig, 'id'>): Promise<LLMConfig> {
    return this.client.post<LLMConfig>('/llm-configs', config);
  }

  async updateLLMConfig(id: string, config: Partial<LLMConfig>): Promise<LLMConfig> {
    return this.client.put<LLMConfig>(`/llm-configs/${id}`, config);
  }

  async deleteLLMConfig(id: string): Promise<void> {
    try {
      await this.client.delete(`/llm-configs/${id}`);
    } catch (error) {
      if (error instanceof Error && error.message.includes('无法删除正在使用的LLM配置')) {
        throw new Error('无法删除正在使用的LLM配置，请先从智能体中移除此配置');
      }
      throw error;
    }
  }

  // ============= 智能体相关 =============

  async getAgents(): Promise<Agent[]> {
    return this.client.get<Agent[]>('/agents');
  }

  async createAgent(agent: Omit<Agent, 'id'>): Promise<Agent> {
    return this.client.post<Agent>('/agents', agent);
  }

  async updateAgent(id: string, agent: Partial<Agent>): Promise<Agent> {
    return this.client.put<Agent>(`/agents/${id}`, agent);
  }

  async deleteAgent(id: string): Promise<void> {
    await this.client.delete(`/agents/${id}`);
  }

  // ============= 讨论相关 =============

  async getDiscussions(): Promise<Discussion[]> {
    return this.client.get<Discussion[]>('/discussions');
  }

  async createDiscussion(discussion: Omit<Discussion, 'createdAt'>): Promise<Discussion> {
    return this.client.post<Discussion>('/discussions', discussion);
  }

  async updateDiscussion(id: string, discussion: Partial<Discussion>): Promise<Discussion> {
    return this.client.put<Discussion>(`/discussions/${id}`, discussion);
  }

  async deleteDiscussion(id: string): Promise<void> {
    await this.client.delete(`/discussions/${id}`);
  }

  async addMessage(discussionId: string, message: Omit<Message, 'id' | 'timestamp'>): Promise<Message> {
    return this.client.post<Message>(`/discussions/${discussionId}/messages`, message);
  }

  // ============= 设置相关 =============

  async getSettings(): Promise<AppSettings> {
    return this.client.get<AppSettings>('/settings');
  }

  async updateSettings(settings: Partial<AppSettings>): Promise<AppSettings> {
    return this.client.put<AppSettings>('/settings', settings);
  }

  async getPreferences(): Promise<UserPreferences> {
    return this.client.get<UserPreferences>('/preferences');
  }

  async updatePreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    return this.client.put<UserPreferences>('/preferences', preferences);
  }

  // ============= 数据管理 =============

  async exportData(): Promise<StorageData> {
    return this.client.get<StorageData>('/data/export');
  }

  async importData(data: Partial<StorageData>, clearExisting: boolean = false): Promise<void> {
    await this.client.post('/data/import', { ...data, clearExisting });
  }

  async clearAllData(): Promise<void> {
    await this.client.delete('/data/clear');
  }

  // 获取存储信息
  async getStorageInfo(): Promise<{ used: number; available: number; total: number }> {
    return this.client.get('/storage/info');
  }
}

// 导出单例实例
export const apiService = new ApiService();
