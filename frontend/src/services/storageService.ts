import { Agent, LLMConfig, Discussion } from '../types';
import { apiService } from './apiService';



// 存储模式枚举
export enum StorageMode {
  SERVER = 'server'
}

// 应用设置接口
export interface AppSettings {
  version: string;
  lastUpdated: string;
  autoSave: boolean;
  maxStoredDiscussions: number;
  defaultDiscussionMode: 'moderator' | 'free';
  theme: 'light' | 'dark' | 'auto';
}

// 用户偏好设置接口
export interface UserPreferences {
  defaultAgentCount: number;
  preferredLLMProvider: string;
  autoStartDiscussion: boolean;
  showAdvancedOptions: boolean;
  notificationsEnabled: boolean;
  exportFormat: 'json' | 'csv' | 'markdown';
}

// 存储数据结构
export interface StorageData {
  agents: Agent[];
  llmConfigs: LLMConfig[];
  discussions: Discussion[];
  settings: AppSettings;
  preferences: UserPreferences;
}

// 数据持久化服务类
export class StorageService {
  private static instance: StorageService;
  private isInitialized = false;
  private storageMode: StorageMode = StorageMode.SERVER;
  private serverAvailable = false;

  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService();
    }
    return StorageService.instance;
  }

  // 初始化存储服务
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('Starting StorageService initialization...');



    // 检查服务器可用性（设置超时）
    console.log('Checking server connection...');
    console.log(`API_BASE_URL: ${import.meta.env.VITE_API_URL}`);

    // 尝试多次连接检查
    let connectionAttempts = 0;
    const maxAttempts = 3;

    while (connectionAttempts < maxAttempts && !this.serverAvailable) {
      connectionAttempts++;
      console.log(`[StorageService] Connection attempt ${connectionAttempts}/${maxAttempts}`);

      try {
        this.serverAvailable = await apiService.checkConnection();
        console.log(`[StorageService] Connection check result: ${this.serverAvailable}`);

        if (this.serverAvailable) {
          break;
        }

        // 如果不是最后一次尝试，等待一段时间再重试
        if (connectionAttempts < maxAttempts) {
          console.log(`[StorageService] Waiting 2 seconds before retry...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } catch (error) {
        console.error(`Server connection check failed (attempt ${connectionAttempts}):`, error);
        this.serverAvailable = false;

        // 如果不是最后一次尝试，等待一段时间再重试
        if (connectionAttempts < maxAttempts) {
          console.log(`[StorageService] Waiting 2 seconds before retry...`);
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    console.log(`Server available: ${this.serverAvailable}`);

    if (!this.serverAvailable) {
      throw new Error('服务器连接失败，请确保后端服务正在运行');
    }

    // 根据服务器可用性设置存储模式
    await this.determineStorageMode();
    console.log(`Storage mode: ${this.storageMode}`);

    // 初始化默认设置
    await this.initializeDefaultSettings();
    console.log('Default settings initialized');

    // 数据迁移（如果需要）
    await this.migrateData();
    console.log('Data migration completed');

    this.isInitialized = true;
    console.log(`StorageService initialized successfully with ${this.storageMode} mode`);
  }



  // 确定存储模式
  private async determineStorageMode(): Promise<void> {
    // 只使用服务器存储模式
    this.storageMode = StorageMode.SERVER;
  }





  // 设置存储模式
  public setStorageMode(mode: StorageMode): void {
    this.storageMode = mode;
    // 注意：由于只使用服务器存储模式，这个方法主要用于内部状态管理
  }

  // 获取存储模式
  public getStorageMode(): StorageMode {
    return this.storageMode;
  }

  // 获取服务器状态
  public isServerAvailable(): boolean {
    return this.serverAvailable;
  }



  // 强制刷新服务器连接状态
  public async refreshServerConnection(): Promise<boolean> {
    this.serverAvailable = await apiService.checkConnection();
    return this.serverAvailable;
  }

  // 初始化默认设置
  private async initializeDefaultSettings(): Promise<void> {
    const defaultSettings: AppSettings = {
      version: '1.0.0',
      lastUpdated: new Date().toISOString(),
      autoSave: true,
      maxStoredDiscussions: 100,
      defaultDiscussionMode: 'free',
      theme: 'light'
    };

    const defaultPreferences: UserPreferences = {
      defaultAgentCount: 3,
      preferredLLMProvider: 'openai',
      autoStartDiscussion: false,
      showAdvancedOptions: false,
      notificationsEnabled: true,
      exportFormat: 'json'
    };

    if (!this.getSettings()) {
      this.saveSettings(defaultSettings);
    }

    if (!this.getPreferences()) {
      this.savePreferences(defaultPreferences);
    }
  }

  // 数据迁移
  private async migrateData(): Promise<void> {
    const settings = await this.getSettings();
    const currentVersion = settings?.version || '0.0.0';
    
    // 这里可以添加版本迁移逻辑
    if (currentVersion < '1.0.0') {
      console.log('Migrating data to version 1.0.0...');
      // 执行迁移操作
    }
  }

  // 智能体相关操作
  async saveAgents(agents: Agent[]): Promise<void> {
    try {
      // 批量保存到服务器
      if (this.serverAvailable) {
        console.log(`[StorageService] Starting batch save for ${agents.length} agents`);

        // 实现批量更新逻辑
        const existingAgents = await this.getAgents();
        const promises = agents.map(async (agent) => {
          const existingIndex = existingAgents.findIndex(a => a.id === agent.id);

          if (existingIndex >= 0) {
            return apiService.updateAgent(agent.id, agent);
          } else {
            return apiService.createAgent(agent);
          }
        });

        await Promise.all(promises);
        console.log(`[StorageService] Successfully saved ${agents.length} agents to server`);
      } else {
        throw new Error('服务器不可用，无法保存智能体数据');
      }

      this.updateLastModified();
    } catch (error) {
      console.error('Failed to save agents:', error);
      throw new Error('Failed to save agents to storage');
    }
  }

  async getAgents(): Promise<Agent[]> {
    // 只使用服务器存储模式
    if (this.serverAvailable) {
      return await apiService.getAgents();
    } else {
      throw new Error('服务器不可用，无法获取智能体数据');
    }
  }



  async saveAgent(agent: Agent): Promise<void> {
    // 只保存到服务器
    if (this.serverAvailable) {
      const agents = await this.getAgents();
      const existingIndex = agents.findIndex(a => a.id === agent.id);

      if (existingIndex >= 0) {
        await apiService.updateAgent(agent.id, agent);
      } else {
        await apiService.createAgent(agent);
      }
    } else {
      throw new Error('服务器不可用，无法保存智能体');
    }
  }

  async deleteAgent(agentId: string): Promise<void> {
    // 只从服务器删除
    if (this.serverAvailable) {
      await apiService.deleteAgent(agentId);
    } else {
      throw new Error('服务器不可用，无法删除智能体');
    }
  }

  // LLM配置相关操作
  async saveLLMConfigs(configs: LLMConfig[]): Promise<void> {
    console.log(`[StorageService] Starting saveLLMConfigs with ${configs.length} configs`);
    console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`);

    try {
      // 批量保存到服务器
      if (this.serverAvailable) {
        console.log('[StorageService] Attempting to save LLM configs to server...');

        // 实现批量更新逻辑
        const existingConfigs = await this.getLLMConfigs();
        const promises = configs.map(async (config) => {
          const existingIndex = existingConfigs.findIndex(c => c.id === config.id);

          if (existingIndex >= 0) {
            return apiService.updateLLMConfig(config.id, config);
          } else {
            return apiService.createLLMConfig(config);
          }
        });

        await Promise.all(promises);
        console.log(`[StorageService] Successfully saved ${configs.length} LLM configs to server`);
      } else {
        throw new Error('服务器不可用，无法保存LLM配置');
      }

      this.updateLastModified();
      console.log('[StorageService] saveLLMConfigs completed successfully');
    } catch (error) {
      console.error('[StorageService] Failed to save LLM configs:', error);
      throw new Error('Failed to save LLM configs to storage');
    }
  }

  async getLLMConfigs(): Promise<LLMConfig[]> {
    // 只使用服务器存储模式
    if (this.serverAvailable) {
      console.log('[StorageService] Using SERVER mode - attempting to load from server');
      const serverConfigs = await apiService.getLLMConfigs();
      console.log(`[StorageService] Successfully loaded ${serverConfigs.length} configs from server`);
      return serverConfigs;
    } else {
      throw new Error('服务器不可用，无法获取LLM配置数据');
    }
  }



  async saveLLMConfig(config: LLMConfig): Promise<void> {
    console.log(`[StorageService] Starting saveLLMConfig for config: ${config.id} (${config.name})`);
    console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`);

    // 只保存到服务器
    if (this.serverAvailable) {
      console.log('[StorageService] Attempting to save single config to server...');
      const configs = await this.getLLMConfigs();
      const existingIndex = configs.findIndex(c => c.id === config.id);
      console.log(`[StorageService] Existing config index: ${existingIndex}`);

      if (existingIndex >= 0) {
        await apiService.updateLLMConfig(config.id, config);
        console.log('[StorageService] Config updated on server successfully');
      } else {
        await apiService.createLLMConfig(config);
        console.log('[StorageService] Config created on server successfully');
      }
      console.log('[StorageService] saveLLMConfig completed successfully');
    } else {
      throw new Error('服务器不可用，无法保存LLM配置');
    }
  }

  async deleteLLMConfig(configId: string): Promise<void> {
    console.log(`[StorageService] Starting deleteLLMConfig for config: ${configId}`);
    console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`);

    // 只从服务器删除
    if (this.serverAvailable) {
      console.log('[StorageService] Attempting to delete config from server...');
      await apiService.deleteLLMConfig(configId);
      console.log('[StorageService] Config deleted from server successfully');
      console.log('[StorageService] deleteLLMConfig completed successfully');
    } else {
      throw new Error('服务器不可用，无法删除LLM配置');
    }
  }

  // LLM配置导入导出方法
  async exportLLMConfigs(): Promise<string> {
    try {
      const configs = await this.getLLMConfigs();
      // 导出时隐藏API密钥
      const exportConfigs = configs.map(config => ({
        ...config,
        apiKey: '***HIDDEN***'
      }));
      return JSON.stringify(exportConfigs, null, 2);
    } catch (error) {
      console.error('Failed to export LLM configs:', error);
      throw new Error('导出LLM配置失败');
    }
  }

  async importLLMConfigs(jsonData: string): Promise<{ success: number; errors: string[] }> {
    const result = { success: 0, errors: [] as string[] };

    try {
      const configs = JSON.parse(jsonData);

      if (!Array.isArray(configs)) {
        throw new Error('数据格式错误：应该是配置数组');
      }

      for (const config of configs) {
        try {
          // 验证配置格式
          if (!config.id || !config.name || !config.provider || !config.model) {
            result.errors.push(`配置 ${config.name || 'Unknown'} 缺少必要字段`);
            continue;
          }

          // 如果API密钥被隐藏，跳过导入
          if (config.apiKey === '***HIDDEN***') {
            result.errors.push(`配置 ${config.name} 的API密钥需要重新设置`);
            continue;
          }

          await this.saveLLMConfig(config);
          result.success++;
        } catch (error) {
          result.errors.push(`导入配置 ${config.name || 'Unknown'} 失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
    } catch (error) {
      result.errors.push('解析JSON数据失败: ' + (error instanceof Error ? error.message : '未知错误'));
    }

    return result;
  }

  // 生成LLM配置ID
  generateLLMConfigId(): string {
    return `llm_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // 创建默认LLM配置
  createDefaultLLMConfig(preset: any, apiKey: string, baseURL?: string): LLMConfig {
    return {
      id: this.generateLLMConfigId(),
      name: preset.name,
      provider: preset.provider.toLowerCase() as LLMConfig['provider'],
      model: preset.model,
      apiKey,
      baseURL,
      temperature: preset.defaultSettings.temperature,
      maxTokens: preset.defaultSettings.maxTokens
    };
  }

  // 获取特定LLM配置
  async getLLMConfig(configId: string): Promise<LLMConfig | null> {
    try {
      const configs = await this.getLLMConfigs();
      return configs.find(c => c.id === configId) || null;
    } catch (error) {
      console.error('Failed to get LLM config:', error);
      return null;
    }
  }

  // 验证LLM配置
  validateLLMConfig(config: Partial<LLMConfig>): string[] {
    const errors: string[] = [];

    if (!config.name?.trim()) {
      errors.push('配置名称不能为空');
    }

    if (!config.provider) {
      errors.push('请选择提供商');
    }

    if (!config.model?.trim()) {
      errors.push('模型名称不能为空');
    }

    if (!config.apiKey?.trim()) {
      errors.push('API密钥不能为空');
    }

    if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 2)) {
      errors.push('温度值应在0-2之间');
    }

    if (config.maxTokens !== undefined && (config.maxTokens < 1 || config.maxTokens > 4000)) {
      errors.push('最大令牌数应在1-4000之间');
    }

    if (config.provider === 'azure' && !config.baseURL?.trim()) {
      errors.push('Azure提供商需要设置基础URL');
    }

    return errors;
  }

  // 获取LLM配置统计
  async getLLMConfigStats(): Promise<{
    total: number;
    byProvider: Record<string, number>;
    recentlyUsed: LLMConfig[];
  }> {
    try {
      const configs = await this.getLLMConfigs();
      const byProvider: Record<string, number> = {};

      configs.forEach(config => {
        byProvider[config.provider] = (byProvider[config.provider] || 0) + 1;
      });

      // 这里可以添加使用记录的逻辑，暂时返回前5个
      const recentlyUsed = configs.slice(0, 5);

      return {
        total: configs.length,
        byProvider,
        recentlyUsed
      };
    } catch (error) {
      console.error('Failed to get LLM config stats:', error);
      return { total: 0, byProvider: {}, recentlyUsed: [] };
    }
  }

  // 讨论记录相关操作
  async saveDiscussions(discussions: Discussion[]): Promise<void> {
    try {
      const settings = await this.getSettings();
      const maxDiscussions = settings?.maxStoredDiscussions || 100;

      // 限制存储的讨论数量，保留最新的
      const limitedDiscussions = discussions
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, maxDiscussions);

      // 批量保存到服务器
      if (this.serverAvailable) {
        console.log(`[StorageService] Starting batch save for ${limitedDiscussions.length} discussions`);

        // 实现批量更新逻辑
        const existingDiscussions = await this.getDiscussions();
        const promises = limitedDiscussions.map(async (discussion) => {
          const existingIndex = existingDiscussions.findIndex(d => d.id === discussion.id);

          if (existingIndex >= 0) {
            return apiService.updateDiscussion(discussion.id, discussion);
          } else {
            return apiService.createDiscussion(discussion);
          }
        });

        await Promise.all(promises);
        console.log(`[StorageService] Successfully saved ${limitedDiscussions.length} discussions to server`);
      } else {
        throw new Error('服务器不可用，无法保存讨论数据');
      }

      this.updateLastModified();
    } catch (error) {
      console.error('Failed to save discussions:', error);
      throw new Error('Failed to save discussions to storage');
    }
  }

  async getDiscussions(): Promise<Discussion[]> {
    // 只使用服务器存储模式
    if (this.serverAvailable) {
      return await apiService.getDiscussions();
    } else {
      throw new Error('服务器不可用，无法获取讨论数据');
    }
  }



  async saveDiscussion(discussion: Discussion): Promise<void> {
    try {
      // 只保存到服务器
      if (this.serverAvailable) {
        const discussions = await this.getDiscussions();
        const existingIndex = discussions.findIndex(d => d.id === discussion.id);

        if (existingIndex >= 0) {
          // 确保传递完整的讨论数据，包括消息
          await apiService.updateDiscussion(discussion.id, discussion as any);
        } else {
          // 创建新讨论时也要包含消息
          await apiService.createDiscussion(discussion as any);
        }
      } else {
        throw new Error('服务器不可用，无法保存讨论');
      }
    } catch (error) {
      console.error('Failed to save discussion:', error);
      throw error;
    }
  }

  async deleteDiscussion(discussionId: string): Promise<void> {
    try {
      // 只从服务器删除
      if (this.serverAvailable) {
        await apiService.deleteDiscussion(discussionId);
      } else {
        throw new Error('服务器不可用，无法删除讨论');
      }
    } catch (error) {
      console.error('Failed to delete discussion:', error);
      throw error;
    }
  }

  // 设置相关操作
  async saveSettings(settings: AppSettings): Promise<void> {
    // 只保存到服务器
    if (this.serverAvailable) {
      await apiService.updateSettings(settings);
    } else {
      throw new Error('服务器不可用，无法保存设置');
    }
  }

  async getSettings(): Promise<AppSettings | null> {
    // 只使用服务器存储模式
    if (this.serverAvailable) {
      return await apiService.getSettings();
    } else {
      throw new Error('服务器不可用，无法获取设置数据');
    }
  }



  // 用户偏好相关操作
  async savePreferences(preferences: UserPreferences): Promise<void> {
    try {
      // 只保存到服务器
      if (this.serverAvailable) {
        await apiService.updatePreferences(preferences);
      } else {
        throw new Error('服务器不可用，无法保存用户偏好');
      }
    } catch (error) {
      console.error('Failed to save preferences:', error);
      throw new Error('Failed to save preferences to storage');
    }
  }

  async getPreferences(): Promise<UserPreferences | null> {
    try {
      // 只使用服务器存储模式
      if (this.serverAvailable) {
        return await apiService.getPreferences();
      } else {
        throw new Error('服务器不可用，无法获取用户偏好数据');
      }
    } catch (error) {
      console.error('Failed to load preferences:', error);
      throw error;
    }
  }



  // 获取所有数据
  async getAllData(): Promise<StorageData> {
    return {
      agents: await this.getAgents(),
      llmConfigs: await this.getLLMConfigs(),
      discussions: await this.getDiscussions(),
      settings: await this.getSettings() || {} as AppSettings,
      preferences: await this.getPreferences() || {} as UserPreferences
    };
  }

  // 导入所有数据
  async importAllData(data: Partial<StorageData>): Promise<void> {
    // 只导入到服务器
    if (this.serverAvailable) {
      await apiService.importData(data, false);
    } else {
      throw new Error('服务器不可用，无法导入数据');
    }
  }

  // 清除所有数据
  async clearAllData(): Promise<void> {
    // 只清除服务器数据
    if (this.serverAvailable) {
      await apiService.clearAllData();
    } else {
      throw new Error('服务器不可用，无法清除数据');
    }
  }



  // 更新最后修改时间
  private async updateLastModified(): Promise<void> {
    const settings = await this.getSettings();
    if (settings) {
      settings.lastUpdated = new Date().toISOString();
      await this.saveSettings(settings);
    }
  }

  // 数据验证
  validateData(data: any): boolean {
    try {
      // 基本的数据结构验证
      if (data.agents && !Array.isArray(data.agents)) return false;
      if (data.llmConfigs && !Array.isArray(data.llmConfigs)) return false;
      if (data.discussions && !Array.isArray(data.discussions)) return false;
      
      return true;
    } catch (error) {
      return false;
    }
  }

  // 获取存储信息
  public async getStorageInfo(): Promise<{ used: number; available: number; total: number }> {
    if (this.serverAvailable) {
      return await apiService.getStorageInfo();
    } else {
      throw new Error('服务器不可用，无法获取存储信息');
    }
  }
}

// 导出单例实例
export const storageService = StorageService.getInstance();
