import { PresetLLMConfig } from '../types';
import { defaultLLMConfigs } from '../data/defaultLLMConfigs';

// 预设的LLM配置
export const presetLLMConfigs: PresetLLMConfig[] = defaultLLMConfigs;



// 工具函数：格式化模型名称
export function formatModelName(model: string): string {
  return model.replace(/[-_]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

// 工具函数：获取提供商图标
export function getProviderIcon(provider: string): string {
  const icons: Record<string, string> = {
    openai: '🤖',
    anthropic: '🧠',
    azure: '☁️',
    custom: '⚙️'
  };
  return icons[provider.toLowerCase()] || '🔧';
}

// 工具函数：获取提供商颜色
export function getProviderColor(provider: string): string {
  const colors: Record<string, string> = {
    openai: 'bg-green-100 text-green-800',
    anthropic: 'bg-blue-100 text-blue-800',
    azure: 'bg-purple-100 text-purple-800',
    custom: 'bg-gray-100 text-gray-800'
  };
  return colors[provider.toLowerCase()] || 'bg-gray-100 text-gray-800';
}
