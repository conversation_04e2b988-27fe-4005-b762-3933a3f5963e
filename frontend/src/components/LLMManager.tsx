import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Download, Upload, Setting<PERSON>, Brain } from 'lucide-react';
import { LLMConfig } from '../types';
import { getProviderIcon, getProviderColor } from '../utils/llmConfig';
import { storageService } from '../services/storageService';
import { LLMConfigModal } from './LLMConfigModal';

export const LLMManager: React.FC = () => {
  const [configs, setConfigs] = useState<LLMConfig[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<LLMConfig | null>(null);
  const [showImportExport, setShowImportExport] = useState(false);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    byProvider: {} as Record<string, number>,
    recentlyUsed: [] as LLMConfig[]
  });

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      setLoading(true);
      const [loadedConfigs, configStats] = await Promise.all([
        storageService.getLLMConfigs(),
        storageService.getLLMConfigStats()
      ]);
      setConfigs(loadedConfigs);
      setStats(configStats);
    } catch (error) {
      console.error('Failed to load configs:', error);
      setConfigs([]);
      setStats({
        total: 0,
        byProvider: {},
        recentlyUsed: []
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveConfig = async (config: LLMConfig) => {
    try {
      await storageService.saveLLMConfig(config);
      await loadConfigs();
    } catch (error) {
      alert('保存配置失败');
    }
  };

  const handleEditConfig = (config: LLMConfig) => {
    setEditingConfig(config);
    setIsModalOpen(true);
  };

  const handleDeleteConfig = async (configId: string) => {
    if (confirm('确定要删除这个LLM配置吗？')) {
      try {
        await storageService.deleteLLMConfig(configId);
        await loadConfigs();
      } catch (error) {
        alert('删除配置失败');
      }
    }
  };

  const handleNewConfig = () => {
    setEditingConfig(null);
    setIsModalOpen(true);
  };

  const handleExportConfigs = async () => {
    try {
      const exportData = await storageService.exportLLMConfigs();
      const blob = new Blob([exportData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `llm-configs-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      alert('导出失败');
    }
  };

  const handleImportConfigs = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const jsonData = e.target?.result as string;
        const result = await storageService.importLLMConfigs(jsonData);

        if (result.success > 0) {
          alert(`成功导入 ${result.success} 个配置`);
          await loadConfigs();
        }

        if (result.errors.length > 0) {
          alert(`导入时遇到错误：\n${result.errors.join('\n')}`);
        }
      } catch (error) {
        alert('导入失败：文件格式错误');
      }
    };
    reader.readAsText(file);

    // 重置文件输入
    event.target.value = '';
  };

  if (loading) {
    return (
      <div className="h-full overflow-y-auto">
        <div className="centered-container">
          <div className="centered-content">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">正在加载LLM配置...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="centered-container">
        <div className="centered-content">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Brain className="w-8 h-8 mr-3 text-blue-600" />
            LLM配置管理
          </h2>
          <p className="text-gray-600 mt-1">
            管理大语言模型配置，为智能体提供AI能力
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowImportExport(!showImportExport)}
            className="flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <Settings className="w-4 h-4 mr-2" />
            导入/导出
          </button>
          <button
            onClick={handleNewConfig}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            新建配置
          </button>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
          <div className="text-sm text-gray-600">总配置数</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-green-600">
            {Object.keys(stats.byProvider).length}
          </div>
          <div className="text-sm text-gray-600">支持的提供商</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-purple-600">
            {stats.recentlyUsed.length}
          </div>
          <div className="text-sm text-gray-600">最近使用</div>
        </div>
      </div>

      {/* 导入/导出面板 */}
      {showImportExport && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
          <h3 className="text-lg font-medium mb-3">导入/导出配置</h3>
          <div className="flex space-x-4">
            <button
              onClick={handleExportConfigs}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <Download className="w-4 h-4 mr-2" />
              导出配置
            </button>
            <label className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
              <Upload className="w-4 h-4 mr-2" />
              导入配置
              <input
                type="file"
                accept=".json"
                onChange={handleImportConfigs}
                className="hidden"
              />
            </label>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            导出的配置文件会隐藏API密钥，导入时需要重新设置
          </p>
        </div>
      )}

      {/* 配置列表 */}
      <div className="bg-white rounded-lg border border-gray-200">
        {configs.length === 0 ? (
          <div className="p-8 text-center">
            <Brain className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              还没有LLM配置
            </h3>
            <p className="text-gray-600 mb-4">
              创建第一个LLM配置来为智能体提供AI能力
            </p>
            <button
              onClick={handleNewConfig}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mx-auto"
            >
              <Plus className="w-4 h-4 mr-2" />
              新建配置
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {configs.map((config) => (
              <div key={config.id} className="p-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">
                      {getProviderIcon(config.provider)}
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        {config.name}
                      </h3>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className={`px-2 py-1 text-xs rounded-full ${getProviderColor(config.provider)}`}>
                          {config.provider.toUpperCase()}
                        </span>
                        <span className="text-sm text-gray-600">
                          {config.model}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-right text-sm text-gray-600">
                      <div>温度: {config.temperature}</div>
                      <div>令牌: {config.maxTokens}</div>
                    </div>
                    <div className="flex space-x-1">
                      <button
                        onClick={() => handleEditConfig(config)}
                        className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md"
                        title="编辑配置"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteConfig(config.id)}
                        className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md"
                        title="删除配置"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
                {config.systemPrompt && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-md">
                    <div className="text-sm text-gray-600">
                      <strong>自定义系统提示词:</strong>
                    </div>
                    <div className="text-sm text-gray-800 mt-1 line-clamp-2">
                      {config.systemPrompt}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 提供商统计 */}
      {Object.keys(stats.byProvider).length > 0 && (
        <div className="mt-6 bg-white p-4 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium mb-3">提供商分布</h3>
          <div className="flex flex-wrap gap-2">
            {Object.entries(stats.byProvider).map(([provider, count]) => (
              <div
                key={provider}
                className={`flex items-center px-3 py-1 rounded-full text-sm ${getProviderColor(provider)}`}
              >
                <span className="mr-1">{getProviderIcon(provider)}</span>
                {provider.toUpperCase()}: {count}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* LLM配置模态框 */}
      <LLMConfigModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingConfig(null);
        }}
        onSave={handleSaveConfig}
        editingConfig={editingConfig}
      />
        </div>
      </div>
    </div>
  );
};
