import React, { useState, useEffect } from 'react';
import { useApp } from '../context/AppContext';
import { storageService } from '../services/storageService';
import {
  Download,
  Upload,
  Trash2,
  Database,
  AlertTriangle,
  CheckCircle,
  Info,
  HardDrive,
  FileText,
  Settings
} from 'lucide-react';

export const DataManager: React.FC = () => {
  const { state, exportData, importData, clearAllData } = useApp();
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importResult, setImportResult] = useState<{ success: boolean; message: string } | null>(null);
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [storageInfo, setStorageInfo] = useState<{ used: number; available: number; total: number } | null>(null);

  // 获取存储信息
  useEffect(() => {
    const fetchStorageInfo = async () => {
      try {
        const info = await storageService.getStorageInfo();
        setStorageInfo(info);
      } catch (error) {
        console.error('Failed to fetch storage info:', error);
        setStorageInfo({ used: 0, available: 0, total: 0 });
      }
    };
    
    fetchStorageInfo();
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      const data = await exportData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `multi-agent-system-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      setImportResult({
        success: false,
        message: '导出失败: ' + (error instanceof Error ? error.message : '未知错误')
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setImportResult(null);

    try {
      const text = await file.text();
      const success = await importData(text);
      
      setImportResult({
        success,
        message: success ? '数据导入成功！' : '数据导入失败，请检查文件格式。'
      });
    } catch (error) {
      setImportResult({
        success: false,
        message: '导入失败: ' + (error instanceof Error ? error.message : '未知错误')
      });
    } finally {
      setIsImporting(false);
      // 重置文件输入
      event.target.value = '';
    }
  };

  const handleClearData = async () => {
    try {
      await clearAllData();
      setShowClearConfirm(false);
      setImportResult({
        success: true,
        message: '所有数据已清除，系统已重置为默认状态。'
      });
    } catch (error) {
      setImportResult({
        success: false,
        message: '清除数据失败: ' + (error instanceof Error ? error.message : '未知错误')
      });
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="centered-container">
        <div className="centered-content">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                <Database className="w-8 h-8 mr-3 text-blue-600" />
                数据管理
              </h2>
              <p className="text-gray-600 mt-1">
                管理系统数据的备份、恢复和清理
              </p>
            </div>
          </div>



      {/* 结果提示 */}
      {importResult && (
        <div className={`mb-6 p-4 rounded-lg border ${
          importResult.success 
            ? 'bg-green-50 border-green-200' 
            : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-center">
            {importResult.success ? (
              <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
            ) : (
              <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
            )}
            <span className={`font-medium ${
              importResult.success ? 'text-green-700' : 'text-red-700'
            }`}>
              {importResult.message}
            </span>
          </div>
        </div>
      )}

      {/* 存储信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {state.agents.length}
              </div>
              <div className="text-sm text-gray-600">智能体</div>
            </div>
            <Settings className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-green-600">
                {state.allDiscussions.length}
              </div>
              <div className="text-sm text-gray-600">历史讨论</div>
            </div>
            <FileText className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {storageInfo ? formatBytes(storageInfo.used) : '加载中...'}
              </div>
              <div className="text-sm text-gray-600">已用存储</div>
            </div>
            <HardDrive className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* 存储使用情况 */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 mb-8">
        <h3 className="text-lg font-medium mb-4">存储使用情况</h3>
        {storageInfo ? (
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>已使用</span>
              <span>{formatBytes(storageInfo.used)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all" 
                style={{ width: `${(storageInfo.used / storageInfo.total) * 100}%` }}
              />
            </div>
            <div className="flex justify-between text-sm text-gray-600">
              <span>可用: {formatBytes(storageInfo.available)}</span>
              <span>总计: {formatBytes(storageInfo.total)}</span>
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500">加载存储信息中...</div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        
        {/* 导出数据 */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center mb-4">
            <Download className="w-6 h-6 text-green-600 mr-3" />
            <h3 className="text-lg font-medium">导出数据</h3>
          </div>
          <p className="text-gray-600 mb-4 text-sm">
            将所有配置和历史记录导出为JSON文件，用于备份或迁移。
          </p>
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Download className="w-4 h-4 mr-2" />
            {isExporting ? '导出中...' : '导出数据'}
          </button>
        </div>

        {/* 导入数据 */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center mb-4">
            <Upload className="w-6 h-6 text-blue-600 mr-3" />
            <h3 className="text-lg font-medium">导入数据</h3>
          </div>
          <p className="text-gray-600 mb-4 text-sm">
            从备份文件恢复配置和历史记录。将覆盖当前数据。
          </p>
          <label className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer">
            <Upload className="w-4 h-4 mr-2" />
            {isImporting ? '导入中...' : '选择文件'}
            <input
              type="file"
              accept=".json"
              onChange={handleImport}
              disabled={isImporting}
              className="hidden"
            />
          </label>
        </div>

        {/* 清除数据 */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center mb-4">
            <Trash2 className="w-6 h-6 text-red-600 mr-3" />
            <h3 className="text-lg font-medium">清除数据</h3>
          </div>
          <p className="text-gray-600 mb-4 text-sm">
            清除所有数据并重置为默认状态。此操作不可撤销。
          </p>
          {!showClearConfirm ? (
            <button
              onClick={() => setShowClearConfirm(true)}
              className="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              清除所有数据
            </button>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                <AlertTriangle className="w-4 h-4 mr-2" />
                确定要清除所有数据吗？
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleClearData}
                  className="flex-1 px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                >
                  确认清除
                </button>
                <button
                  onClick={() => setShowClearConfirm(false)}
                  className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
                >
                  取消
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 使用说明 */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Info className="w-5 h-5 text-blue-500 mr-2 mt-0.5" />
          <div className="text-sm text-blue-700">
            <div className="font-medium mb-1">使用说明：</div>
            <ul className="space-y-1 text-xs">
              <li>• 导出的数据包含智能体配置、LLM配置、讨论历史等所有信息</li>
              <li>• 导入数据会覆盖当前所有配置，建议先导出备份</li>
              <li>• 清除数据会删除所有自定义配置，但会保留默认智能体</li>
              <li>• 数据存储在浏览器本地，清除浏览器数据会丢失所有配置</li>
            </ul>
          </div>
        </div>
      </div>
        </div>
      </div>
    </div>
  );
};
