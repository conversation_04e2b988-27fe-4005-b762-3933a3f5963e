import { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      const isServerConnectionError = this.state.error?.message?.includes('无法连接到后端服务器');
      
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center">
          <div className="text-center max-w-2xl mx-auto p-8">
            <div className="flex items-center justify-center gap-3 mb-6">
              <AlertTriangle size={48} className="text-red-500" />
              <h1 className="text-3xl font-bold text-gray-900">
                {isServerConnectionError ? '无法连接后端服务' : '系统初始化失败'}
              </h1>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">错误详情</h2>
              <div className="text-left bg-gray-50 rounded p-4 mb-4">
                <p className="text-red-600 font-mono text-sm">
                  {this.state.error?.message || '未知错误'}
                </p>
              </div>
              
              <div className="text-sm text-gray-600 mb-4">
                {isServerConnectionError ? (
                  <>
                    <p className="font-medium mb-2">请检查以下项目：</p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>后端服务是否已启动（端口5000）</li>
                      <li>网络连接是否正常</li>
                      <li>防火墙是否阻止了连接</li>
                      <li>后端服务地址配置是否正确</li>
                    </ul>
                  </>
                ) : (
                  <>
                    <p>可能的原因：</p>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>网络连接问题</li>
                      <li>后端服务器不可用</li>
                      <li>浏览器存储空间不足</li>
                      <li>配置文件损坏</li>
                    </ul>
                  </>
                )}
              </div>
            </div>
            
            <div className="flex gap-4 justify-center">
              <button
                onClick={this.handleReload}
                className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <RefreshCw size={20} />
                重新加载页面
              </button>
              
              <button
                onClick={this.handleReset}
                className="flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                重试初始化
              </button>
            </div>
            
            {isServerConnectionError && (
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-blue-800 font-medium mb-2">启动后端服务：</p>
                <div className="text-sm text-blue-700 space-y-1">
                  <p>Windows: 运行 <code className="bg-blue-100 px-1 rounded">start.bat</code></p>
                  <p>Linux/macOS: 运行 <code className="bg-blue-100 px-1 rounded">./start.sh</code></p>
                </div>
              </div>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
