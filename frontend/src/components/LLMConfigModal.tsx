import React, { useState, useEffect } from 'react';
import { X, Save, TestTube, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';
import { LLMConfig } from '../types';
import { presetLLMConfigs } from '../utils/llmConfig';
import { storageService } from '../services/storageService';
import { llmService } from '../services/llmService';

interface LLMConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: LLMConfig) => void;
  editingConfig?: LLMConfig | null;
}

export const LLMConfigModal: React.FC<LLMConfigModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingConfig
}) => {
  const [config, setConfig] = useState<Partial<LLMConfig>>({
    name: '',
    provider: 'openai',
    model: '',
    apiKey: '',
    baseURL: '',
    temperature: 0.7,
    maxTokens: 1000,
    systemPrompt: ''
  });
  const [showApiKey, setShowApiKey] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [errors, setErrors] = useState<string[]>([]);
  const [selectedPreset, setSelectedPreset] = useState<string>('');

  useEffect(() => {
    if (editingConfig) {
      setConfig(editingConfig);
      setSelectedPreset('');
    } else {
      setConfig({
        name: '',
        provider: 'openai',
        model: '',
        apiKey: '',
        baseURL: '',
        temperature: 0.7,
        maxTokens: 1000,
        systemPrompt: ''
      });
      setSelectedPreset('');
    }
    setTestResult(null);
    setErrors([]);
  }, [editingConfig, isOpen]);

  const handlePresetChange = (presetId: string) => {
    setSelectedPreset(presetId);
    if (presetId) {
      const preset = presetLLMConfigs.find(p => p.id === presetId);
      if (preset) {
        setConfig(prev => ({
          ...prev,
          name: preset.name,
          provider: preset.provider.toLowerCase() as LLMConfig['provider'],
          model: preset.model,
          temperature: preset.defaultSettings.temperature,
          maxTokens: preset.defaultSettings.maxTokens
        }));
      }
    }
  };

  const handleInputChange = (field: keyof LLMConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }));
    setTestResult(null);
    
    // 清除相关错误
    if (errors.length > 0) {
      const newErrors = storageService.validateLLMConfig({ ...config, [field]: value });
      setErrors(newErrors);
    }
  };

  const handleTestConnection = async () => {
    const validationErrors = storageService.validateLLMConfig(config);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsTestingConnection(true);
    setTestResult(null);

    try {
      const testConfig: LLMConfig = {
        id: 'test',
        name: config.name!,
        provider: config.provider!,
        model: config.model!,
        apiKey: config.apiKey!,
        baseURL: config.baseURL,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        systemPrompt: config.systemPrompt
      };

      const success = await llmService.testLLMConfig(testConfig);
      setTestResult({
        success,
        message: success ? '连接测试成功！' : '连接测试失败，请检查配置。'
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSave = () => {
    const validationErrors = storageService.validateLLMConfig(config);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    const finalConfig: LLMConfig = {
      id: editingConfig?.id || storageService.generateLLMConfigId(),
      name: config.name!,
      provider: config.provider!,
      model: config.model!,
      apiKey: config.apiKey!,
      baseURL: config.baseURL,
      temperature: config.temperature,
      maxTokens: config.maxTokens,
      systemPrompt: config.systemPrompt
    };

    onSave(finalConfig);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold">
            {editingConfig ? '编辑LLM配置' : '新建LLM配置'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 预设配置选择 */}
        {!editingConfig && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择预设配置（可选）
            </label>
            <select
              value={selectedPreset}
              onChange={(e) => handlePresetChange(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">自定义配置</option>
              {presetLLMConfigs.map(preset => (
                <option key={preset.id} value={preset.id}>
                  {preset.name} - {preset.description}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* 错误提示 */}
        {errors.length > 0 && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700 font-medium">配置错误</span>
            </div>
            <ul className="mt-2 text-sm text-red-600">
              {errors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* 测试结果 */}
        {testResult && (
          <div className={`mb-4 p-3 border rounded-md ${
            testResult.success 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center">
              {testResult.success ? (
                <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
              ) : (
                <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              )}
              <span className={`font-medium ${
                testResult.success ? 'text-green-700' : 'text-red-700'
              }`}>
                {testResult.message}
              </span>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {/* 配置名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              配置名称 *
            </label>
            <input
              type="text"
              value={config.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="例如：我的GPT-4配置"
            />
          </div>

          {/* 提供商 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              提供商 *
            </label>
            <select
              value={config.provider || 'openai'}
              onChange={(e) => handleInputChange('provider', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="azure">Azure OpenAI</option>
              <option value="custom">自定义</option>
            </select>
          </div>

          {/* 模型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              模型名称 *
            </label>
            <input
              type="text"
              value={config.model || ''}
              onChange={(e) => handleInputChange('model', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="例如：gpt-4, claude-3-opus-20240229"
            />
          </div>

          {/* API密钥 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              API密钥 *
            </label>
            <div className="relative">
              <input
                type={showApiKey ? 'text' : 'password'}
                value={config.apiKey || ''}
                onChange={(e) => handleInputChange('apiKey', e.target.value)}
                className="w-full p-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="输入API密钥"
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="absolute right-2 top-2 text-gray-500 hover:text-gray-700"
              >
                {showApiKey ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* 基础URL */}
          {(config.provider === 'azure' || config.provider === 'custom') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                基础URL {config.provider === 'azure' ? '*' : '(可选)'}
              </label>
              <input
                type="text"
                value={config.baseURL || ''}
                onChange={(e) => handleInputChange('baseURL', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder={config.provider === 'azure' 
                  ? "https://your-resource.openai.azure.com" 
                  : "https://api.example.com"
                }
              />
            </div>
          )}

          {/* 高级设置 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                温度 (0-2)
              </label>
              <input
                type="number"
                min="0"
                max="2"
                step="0.1"
                value={config.temperature || 0.7}
                onChange={(e) => handleInputChange('temperature', parseFloat(e.target.value))}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                最大令牌数
              </label>
              <input
                type="number"
                min="1"
                max="4000"
                value={config.maxTokens || 1000}
                onChange={(e) => handleInputChange('maxTokens', parseInt(e.target.value))}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* 系统提示词 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              自定义系统提示词 (可选)
            </label>
            <textarea
              value={config.systemPrompt || ''}
              onChange={(e) => handleInputChange('systemPrompt', e.target.value)}
              rows={3}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="添加额外的系统提示词..."
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-between mt-6">
          <button
            onClick={handleTestConnection}
            disabled={isTestingConnection}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <TestTube className="w-4 h-4 mr-2" />
            {isTestingConnection ? '测试中...' : '测试连接'}
          </button>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              <Save className="w-4 h-4 mr-2" />
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
