import React, { useState, useEffect } from 'react';
import { Brain, Loader, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { useApp } from '../context/AppContext';

interface LoadingStep {
  id: string;
  label: string;
  status: 'pending' | 'loading' | 'completed' | 'error';
}

export const LoadingScreen: React.FC = () => {
  const { state } = useApp();
  const [timeoutWarning, setTimeoutWarning] = useState(false);
  
  const [steps, setSteps] = useState<LoadingStep[]>([
    { id: 'storage', label: '初始化存储服务', status: 'pending' },
    { id: 'server', label: '检查服务器连接', status: 'pending' },
    { id: 'agents', label: '加载智能体配置', status: 'pending' },
    { id: 'llm', label: '加载LLM配置', status: 'pending' },
    { id: 'discussions', label: '加载讨论历史', status: 'pending' },
  ]);

  // 根据实际加载步骤更新状态
  useEffect(() => {
    setSteps(current =>
      current.map(step => {
        const stepIndex = current.findIndex(s => s.id === step.id);
        const currentStepIndex = current.findIndex(s => s.id === state.loadingStep);
        
        if (stepIndex < currentStepIndex) {
          return { ...step, status: 'completed' };
        } else if (stepIndex === currentStepIndex) {
          return { ...step, status: 'loading' };
        } else {
          return { ...step, status: 'pending' };
        }
      })
    );
  }, [state.loadingStep]);

  // 超时警告逻辑
  useEffect(() => {
    const timer = setTimeout(() => {
      setTimeoutWarning(true);
    }, 10000); // 10秒后显示警告

    return () => clearTimeout(timer);
  }, []);

  const getStepIcon = (status: LoadingStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'loading':
        return <Loader className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const progress = (completedSteps / steps.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto">
        {/* Logo和标题 */}
        <div className="flex items-center justify-center gap-3 mb-8">
          <Brain size={48} className="text-blue-600 animate-pulse" />
          <h1 className="text-4xl font-bold text-gray-900">多智能体讨论系统</h1>
        </div>

        {/* 加载动画 */}
        <div className="flex items-center justify-center gap-3 mb-6">
          <Loader className="w-6 h-6 text-blue-600 animate-spin" />
          <span className="text-lg text-gray-600">正在初始化系统...</span>
        </div>

        {/* 加载进度条 */}
        <div className="w-80 mx-auto mb-6">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-500"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <div className="mt-2 text-sm text-gray-500">
            {completedSteps}/{steps.length} 步骤完成
          </div>
        </div>

        {/* 加载步骤 */}
        <div className="space-y-3 text-sm">
          {steps.map((step) => (
            <div key={step.id} className="flex items-center justify-center gap-3">
              {getStepIcon(step.status)}
              <span className={`${
                step.status === 'completed' ? 'text-green-600' :
                step.status === 'loading' ? 'text-blue-600' :
                step.status === 'error' ? 'text-red-600' :
                'text-gray-500'
              }`}>
                {step.label}
              </span>
            </div>
          ))}
        </div>

        {/* 超时警告 */}
        {timeoutWarning && (
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">加载时间较长</span>
            </div>
            <p className="text-sm text-yellow-700 mt-1">
              连接服务器超时，原因是网络较慢或后台服务不可用。
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
