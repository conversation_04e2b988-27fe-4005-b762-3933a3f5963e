import { useState } from 'react';
import { useApp } from '../context/AppContext';
import { DiscussionConfig } from '../types';
import { MessageCircle, Users, Settings, Play, AlertCircle } from 'lucide-react';
export default function DiscussionSetup() {
  const { state, startDiscussion } = useApp();
  const [config, setConfig] = useState<DiscussionConfig>({
    topic: '',
    mode: 'free',
    selectedAgents: [],
    moderatorId: undefined,
  });
  const [errors, setErrors] = useState<string[]>([]);
  const validateConfig = (): boolean => {
    const newErrors: string[] = [];
    if (!config.topic.trim()) {
      newErrors.push('请输入讨论话题');
    }
    if (config.selectedAgents.length < 2) {
      newErrors.push('至少需要选择2个智能体参与讨论');
    }
    if (config.selectedAgents.length > 8) {
      newErrors.push('最多支持8个智能体同时讨论');
    }

    // 检查主持人模式下是否选择了主持人
    if (config.mode === 'moderator' && !config.moderatorId) {
      const availableModerators = config.selectedAgents
        .map(agentId => activeAgents.find(agent => agent.id === agentId))
        .filter(agent => agent && agent.isModerator);

      if (availableModerators.length === 0) {
        newErrors.push('主持人模式需要至少一个具备主持人能力的智能体');
      } else {
        newErrors.push('主持人模式下请选择一个主持人');
      }
    }

    setErrors(newErrors);
    return newErrors.length === 0;
  };
  const handleStartDiscussion = () => {
    if (validateConfig()) {
      startDiscussion(config);
    }
  };
  const toggleAgent = (agentId: string) => {
    setConfig(prev => ({
      ...prev,
      selectedAgents: prev.selectedAgents.includes(agentId)
        ? prev.selectedAgents.filter(id => id !== agentId)
        : [...prev.selectedAgents, agentId]
    }));
  };
  const activeAgents = state.agents.filter(agent => agent.isActive);
  if (activeAgents.length === 0) {
    return (
      <div className="h-full bg-gradient-to-br from-orange-50 to-red-50 overflow-y-auto">
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <AlertCircle size={64} className="text-orange-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">没有可用的智能体</h2>
            <p className="text-gray-600 mb-6">
              您需要先创建和配置智能体才能开始讨论。
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              前往智能体管理
            </button>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="h-full bg-gradient-to-br from-purple-50 to-pink-50 w-full overflow-y-auto">
      <div className="flex justify-center p-6">
        <div className="bg-white rounded-xl shadow-lg overflow-hidden" style={{width: '800px', maxWidth: '800px'}}>
          {/* 头部 */}
          <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8">
            <div className="flex items-center gap-3 mb-4">
              <MessageCircle size={32} />
              <h1 className="text-3xl font-bold">创建新讨论</h1>
            </div>
            <p className="text-purple-100">
              配置讨论话题、模式和参与者，开始智能体之间的协作讨论
            </p>
          </div>
          <div className="p-8 space-y-8">
            {/* 错误提示 */}
            {errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle size={20} className="text-red-600" />
                  <h3 className="font-medium text-red-800">配置错误</h3>
                </div>
                <ul className="text-red-700 text-sm space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
            {/* 讨论话题 */}
            <div className="space-y-3">
              <label className="block text-lg font-semibold text-gray-900">
                讨论话题
              </label>
              <textarea
                value={config.topic}
                onChange={(e) => setConfig(prev => ({ ...prev, topic: e.target.value }))}
                placeholder="请输入您想要讨论的话题，例如：如何提升用户体验设计质量？"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                rows={3}
              />
              <p className="text-sm text-gray-500">
                清晰的话题描述有助于智能体更好地理解和参与讨论
              </p>
            </div>
            {/* 讨论模式 */}
            <div className="space-y-4">
              <label className="block text-lg font-semibold text-gray-900">
                讨论模式
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => setConfig(prev => ({ ...prev, mode: 'free' }))}
                  className={`p-6 rounded-xl border-2 transition-all text-left ${
                    config.mode === 'free'
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <MessageCircle size={24} className={config.mode === 'free' ? 'text-purple-600' : 'text-gray-600'} />
                    <h3 className="font-semibold text-gray-900">自由讨论模式</h3>
                  </div>
                  <p className="text-gray-600 text-sm">
                    智能体根据话题相关性和兴趣自主发言，讨论更加自然流畅
                  </p>
                </button>
                <button
                  onClick={() => setConfig(prev => ({ ...prev, mode: 'moderator' }))}
                  className={`p-6 rounded-xl border-2 transition-all text-left ${
                    config.mode === 'moderator'
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <Settings size={24} className={config.mode === 'moderator' ? 'text-purple-600' : 'text-gray-600'} />
                    <h3 className="font-semibold text-gray-900">主持人模式</h3>
                  </div>
                  <p className="text-gray-600 text-sm">
                    选择一个智能体作为主持人，按轮次组织讨论，更加有序规范
                  </p>
                </button>
              </div>
            </div>
            {/* 参与者选择 */}
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Users size={24} className="text-purple-600" />
                <h2 className="text-lg font-semibold text-gray-900">
                  选择参与者 ({config.selectedAgents.length}/8)
                </h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {activeAgents.map((agent) => (
                  <button
                    key={agent.id}
                    onClick={() => toggleAgent(agent.id)}
                    className={`p-4 rounded-xl border-2 transition-all text-left ${
                      config.selectedAgents.includes(agent.id)
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <img
                        src={agent.avatar}
                        alt={agent.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div>
                        <h3 className="font-medium text-gray-900">{agent.name}</h3>
                        <p className="text-sm text-gray-500">
                          {agent.expertise.slice(0, 2).join('、')}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          {agent.thinkingStyle === 'logical' && '逻辑型'}
                          {agent.thinkingStyle === 'creative' && '创意型'}
                          {agent.thinkingStyle === 'analytical' && '分析型'}
                          {agent.thinkingStyle === 'intuitive' && '直觉型'}
                          {agent.thinkingStyle === 'systematic' && '系统型'}
                        </span>
                        {agent.isModerator && (
                          <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">
                            主持人
                          </span>
                        )}
                      </div>

                      {config.selectedAgents.includes(agent.id) && (
                        <div className="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full"></div>
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
              <p className="text-sm text-gray-500">
                建议选择具有不同专业背景和思维方式的智能体，以获得更丰富的讨论视角
              </p>
            </div>

            {/* 主持人选择 */}
            {config.selectedAgents.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Settings size={24} className="text-purple-600" />
                  <h3 className="text-lg font-semibold text-gray-900">主持人设置</h3>
                </div>

                <div className="space-y-3">
                  <label className="block text-sm font-medium text-gray-700">
                    选择主持人（可选）
                  </label>
                  <select
                    value={config.moderatorId || ''}
                    onChange={(e) => setConfig(prev => ({
                      ...prev,
                      moderatorId: e.target.value || undefined
                    }))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="">无主持人（系统自动管理）</option>
                    {config.selectedAgents
                      .map(agentId => activeAgents.find(agent => agent.id === agentId))
                      .filter(agent => agent && agent.isModerator)
                      .map(agent => (
                        <option key={agent!.id} value={agent!.id}>
                          {agent!.name} - {agent!.expertise.slice(0, 2).join('、')}
                        </option>
                      ))}
                  </select>

                  {config.moderatorId && (
                    <div className="space-y-4">
                      {/* 主持人预览 */}
                      {(() => {
                        const selectedModerator = activeAgents.find(agent => agent.id === config.moderatorId);
                        return selectedModerator ? (
                          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-start gap-4">
                              <img
                                src={selectedModerator.avatar}
                                alt={selectedModerator.name}
                                className="w-12 h-12 rounded-full object-cover"
                              />
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-semibold text-gray-900">{selectedModerator.name}</h4>
                                  <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">主持人</span>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                  <div>
                                    <span className="text-gray-600">专业领域：</span>
                                    <span className="text-gray-900">{selectedModerator.expertise.slice(0, 3).join('、')}</span>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">思维方式：</span>
                                    <span className="text-gray-900">
                                      {selectedModerator.thinkingStyle === 'logical' && '逻辑型'}
                                      {selectedModerator.thinkingStyle === 'creative' && '创意型'}
                                      {selectedModerator.thinkingStyle === 'analytical' && '分析型'}
                                      {selectedModerator.thinkingStyle === 'intuitive' && '直觉型'}
                                      {selectedModerator.thinkingStyle === 'systematic' && '系统型'}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">性格特征：</span>
                                    <span className="text-gray-900">
                                      {selectedModerator.personality === 'assertive' && '果断型'}
                                      {selectedModerator.personality === 'collaborative' && '协作型'}
                                      {selectedModerator.personality === 'diplomatic' && '外交型'}
                                      {selectedModerator.personality === 'direct' && '直接型'}
                                      {selectedModerator.personality === 'thoughtful' && '深思型'}
                                    </span>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">管理风格：</span>
                                    <span className="text-gray-900">
                                      {config.moderatorConfig?.managementStyle === 'strict' && '严格型'}
                                      {config.moderatorConfig?.managementStyle === 'flexible' && '灵活型'}
                                      {config.moderatorConfig?.managementStyle === 'collaborative' && '协作型'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : null;
                      })()}

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <Settings size={16} className="text-blue-600" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium text-blue-900 mb-1">主持人职责</h4>
                            <ul className="text-sm text-blue-700 space-y-1">
                              <li>• 实时总结讨论内容</li>
                              <li>• 监控话题相关性</li>
                              <li>• 指定发言顺序（主持人模式）</li>
                              <li>• 引导讨论方向</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      {/* 主持人配置选项 */}
                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h4 className="font-medium text-gray-900 mb-3">主持人配置</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              总结频率
                            </label>
                            <select
                              value={config.moderatorConfig?.summaryFrequency || 5}
                              onChange={(e) => setConfig(prev => ({
                                ...prev,
                                moderatorConfig: {
                                  ...prev.moderatorConfig,
                                  summaryFrequency: parseInt(e.target.value),
                                  interventionThreshold: prev.moderatorConfig?.interventionThreshold || 0.6,
                                  managementStyle: prev.moderatorConfig?.managementStyle || 'flexible',
                                  autoTerminate: prev.moderatorConfig?.autoTerminate || true,
                                  maxInterventions: prev.moderatorConfig?.maxInterventions || 5
                                }
                              }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            >
                              <option value={3}>每3条消息</option>
                              <option value={5}>每5条消息</option>
                              <option value={8}>每8条消息</option>
                              <option value={10}>每10条消息</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              管理风格
                            </label>
                            <select
                              value={config.moderatorConfig?.managementStyle || 'flexible'}
                              onChange={(e) => setConfig(prev => ({
                                ...prev,
                                moderatorConfig: {
                                  ...prev.moderatorConfig,
                                  summaryFrequency: prev.moderatorConfig?.summaryFrequency || 5,
                                  interventionThreshold: prev.moderatorConfig?.interventionThreshold || 0.6,
                                  managementStyle: e.target.value as 'strict' | 'flexible' | 'collaborative',
                                  autoTerminate: prev.moderatorConfig?.autoTerminate || true,
                                  maxInterventions: prev.moderatorConfig?.maxInterventions || 5
                                }
                              }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            >
                              <option value="strict">严格型 - 严格控制发言顺序</option>
                              <option value="flexible">灵活型 - 适度引导讨论</option>
                              <option value="collaborative">协作型 - 鼓励自由交流</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              干预阈值
                            </label>
                            <select
                              value={config.moderatorConfig?.interventionThreshold || 0.6}
                              onChange={(e) => setConfig(prev => ({
                                ...prev,
                                moderatorConfig: {
                                  ...prev.moderatorConfig,
                                  summaryFrequency: prev.moderatorConfig?.summaryFrequency || 5,
                                  interventionThreshold: parseFloat(e.target.value),
                                  managementStyle: prev.moderatorConfig?.managementStyle || 'flexible',
                                  autoTerminate: prev.moderatorConfig?.autoTerminate || true,
                                  maxInterventions: prev.moderatorConfig?.maxInterventions || 5
                                }
                              }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            >
                              <option value={0.8}>高敏感度 - 轻微偏题即干预</option>
                              <option value={0.6}>中等敏感度 - 适度偏题时干预</option>
                              <option value={0.4}>低敏感度 - 严重偏题才干预</option>
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              最大干预次数
                            </label>
                            <select
                              value={config.moderatorConfig?.maxInterventions || 5}
                              onChange={(e) => setConfig(prev => ({
                                ...prev,
                                moderatorConfig: {
                                  ...prev.moderatorConfig,
                                  summaryFrequency: prev.moderatorConfig?.summaryFrequency || 5,
                                  interventionThreshold: prev.moderatorConfig?.interventionThreshold || 0.6,
                                  managementStyle: prev.moderatorConfig?.managementStyle || 'flexible',
                                  autoTerminate: prev.moderatorConfig?.autoTerminate || true,
                                  maxInterventions: parseInt(e.target.value)
                                }
                              }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            >
                              <option value={3}>3次</option>
                              <option value={5}>5次</option>
                              <option value={8}>8次</option>
                              <option value={-1}>不限制</option>
                            </select>
                          </div>
                        </div>

                        <div className="mt-4">
                          <label className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={config.moderatorConfig?.autoTerminate || true}
                              onChange={(e) => setConfig(prev => ({
                                ...prev,
                                moderatorConfig: {
                                  ...prev.moderatorConfig,
                                  summaryFrequency: prev.moderatorConfig?.summaryFrequency || 5,
                                  interventionThreshold: prev.moderatorConfig?.interventionThreshold || 0.6,
                                  managementStyle: prev.moderatorConfig?.managementStyle || 'flexible',
                                  autoTerminate: e.target.checked,
                                  maxInterventions: prev.moderatorConfig?.maxInterventions || 5
                                }
                              }))}
                              className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                            />
                            <span className="text-sm text-gray-700">
                              允许主持人自动终止讨论
                            </span>
                          </label>
                        </div>
                      </div>
                    </div>
                  )}

                  <p className="text-sm text-gray-500">
                    主持人将负责管理讨论流程，确保讨论高效有序进行
                  </p>
                </div>
              </div>
            )}

            {/* 高级设置 */}
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">高级设置（可选）</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    最大消息数量
                  </label>
                  <input
                    type="number"
                    min="10"
                    max="100"
                    value={config.maxMessages || ''}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      maxMessages: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                    placeholder="不限制"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    时间限制（分钟）
                  </label>
                  <input
                    type="number"
                    min="5"
                    max="120"
                    value={config.timeLimit || ''}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      timeLimit: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                    placeholder="不限制"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
            {/* 开始按钮 */}
            <div className="flex justify-center pt-6">
              <button
                onClick={handleStartDiscussion}
                disabled={!config.topic.trim() || config.selectedAgents.length < 2}
                className="flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all shadow-lg text-lg font-medium"
              >
                <Play size={24} />
                开始讨论
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
