# 多智能体讨论系统 - 快速启动指南

## 🎯 问题解决

**问题**: 清理cookie之后所有数据都没有了
**解决方案**: 现在支持服务器端数据存储，数据不会因为清理浏览器而丢失！

## 🚀 快速启动

### 方式一：一键启动（推荐）

**Linux/macOS:**
```bash
./start.sh
```

**Windows:**
```cmd
start.bat
```

启动脚本会自动完成所有设置，包括：
- ✅ 检查依赖
- ✅ 安装前后端依赖
- ✅ 初始化数据库
- ✅ 启动服务

### 方式二：手动启动

**1. 启动后端服务**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python init_db.py
python app.py
```

**2. 启动前端服务**
```bash
npm install
npm run dev
```

## 🌐 访问地址

- **前端**: http://localhost:3000
- **后端**: http://localhost:5000

## 📊 存储模式

系统使用服务器存储模式：

#### 🌐 服务器存储
- 数据保存在服务器数据库
- 永久保存，不会丢失
- 需要后端服务运行
- 确保数据一致性和安全性

### 3. 数据管理
- **数据导出**: 将服务器数据导出为备份文件
- **数据导入**: 从备份文件恢复数据到服务器

## 🤖 使用流程

### 1. 配置LLM
1. 进入"LLM管理"页面
2. 点击"添加LLM配置"
3. 填写API密钥和配置信息
4. 测试连接确保正常

### 2. 创建智能体
1. 进入"智能体管理"页面
2. 点击"添加智能体"
3. 配置智能体属性
4. 分配LLM配置

### 3. 开始讨论
1. 进入"创建讨论"页面
2. 设置讨论主题
3. 选择参与的智能体
4. 开始讨论

## 🔧 故障排除

### 后端服务无法启动
1. 检查Python版本（需要3.8+）
2. 确保虚拟环境已激活
3. 检查端口5000是否被占用

### 前端无法连接后端
1. 确认后端服务正在运行
2. 检查防火墙设置
3. 验证API地址配置

### 数据同步失败
1. 检查网络连接
2. 确认后端服务状态
3. 查看浏览器控制台错误信息

## 📁 项目结构

```
multi_chat/
├── src/                    # 前端源码
│   ├── components/         # React组件
│   ├── services/          # 服务层
│   └── context/           # 状态管理
├── backend/               # 后端源码
│   ├── app.py            # Flask应用
│   ├── models.py         # 数据模型
│   ├── routes.py         # API路由
│   └── init_db.py        # 数据库初始化
├── docs/                 # 文档
├── start.sh             # Linux/macOS启动脚本
└── start.bat            # Windows启动脚本
```

## 🎉 完成！

现在你的多智能体讨论系统已经具备了完整的数据持久化功能：

- ✅ 数据不会因清理浏览器而丢失
- ✅ 服务器端安全存储
- ✅ 自动数据保存
- ✅ 完整的备份恢复功能

享受你的AI智能体讨论之旅吧！🚀

## 📞 技术支持

如果遇到问题，请查看：
- `docs/README.md` - 详细文档
- `docs/SERVER_STORAGE_IMPLEMENTATION.md` - 技术实现说明
- `backend/README.md` - 后端API文档
