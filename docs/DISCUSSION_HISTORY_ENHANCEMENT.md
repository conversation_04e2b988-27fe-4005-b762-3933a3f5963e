# 讨论历史页面增强功能

## 功能概述
在讨论历史页面中为每一条话题记录添加了"转到聊天室"按钮，方便用户查看历史讨论的所有消息。

## 修改内容

### 1. AppContext.tsx 修改
- 添加了 `SET_CURRENT_DISCUSSION` action 类型
- 在 AppAction 类型中增加了新的 action
- 在 AppContext 接口中添加了 `setCurrentDiscussion` 函数
- 在 reducer 中添加了处理 `SET_CURRENT_DISCUSSION` 的逻辑
- 实现了 `setCurrentDiscussion` 函数
- 在 Provider 的 value 中导出了 `setCurrentDiscussion` 函数

### 2. DiscussionHistory.tsx 修改
- 添加了 `ExternalLink` 图标导入
- 增加了 `DiscussionHistoryProps` 接口，包含 `onNavigate` 参数
- 从 `useApp` 中解构出 `setCurrentDiscussion` 函数
- 实现了 `handleViewInChatRoom` 函数，用于设置当前讨论并导航到讨论室
- 在每个讨论记录的操作按钮区域添加了"转到聊天室"按钮

### 3. App.tsx 修改
- 在渲染 `DiscussionHistory` 组件时传递了 `onNavigate` 参数

### 4. DiscussionRoom.tsx 修改
- 修改了讨论头部，根据 `state.isDiscussionActive` 显示不同的标题和样式
- 历史讨论显示为"历史讨论记录"，活跃讨论显示为"讨论进行中"
- 调整了颜色主题：历史讨论使用灰色主题，活跃讨论使用蓝色主题
- 修改了右上角信息显示：历史讨论显示创建时间，活跃讨论显示讨论时长和结束按钮
- 优化了讨论时长计算逻辑：
  - 活跃讨论：从创建时间到当前时间
  - 历史讨论：从创建时间到最后一条消息时间
- 确保历史讨论不会启动模拟功能
- 隐藏历史讨论中的"智能体正在思考中..."提示
- 只在活跃讨论中更新共识度

## 功能特点

### 用户体验
1. **一键跳转**：在讨论历史页面点击"转到聊天室"按钮即可查看完整的历史对话
2. **视觉区分**：历史讨论和活跃讨论在界面上有明显的视觉区分
3. **信息完整**：历史讨论中显示所有原始信息，包括参与者、消息、统计数据等

### 技术实现
1. **状态管理**：通过 AppContext 管理当前讨论状态
2. **导航控制**：通过 onNavigate 回调实现页面间导航
3. **条件渲染**：根据讨论状态动态调整界面显示
4. **数据完整性**：保持历史数据的完整性，不进行实时更新

## 使用方法

1. 进入"讨论历史"页面
2. 找到想要查看的历史讨论记录
3. 点击该记录右侧的"转到聊天室"按钮（外链图标）
4. 系统会自动跳转到讨论室页面，显示该历史讨论的完整内容
5. 在讨论室中可以查看所有消息、参与者信息、统计数据等

## 测试建议

用户可以通过以下方式测试功能：

1. **创建测试讨论**：
   ```
   - 创建几个智能体
   - 启动一个讨论并让其运行一段时间
   - 结束讨论，使其成为历史记录
   ```

2. **测试历史查看**：
   ```
   - 进入讨论历史页面
   - 点击"转到聊天室"按钮
   - 验证是否正确显示历史讨论内容
   - 确认界面显示为"历史讨论记录"而非"讨论进行中"
   ```

3. **验证功能完整性**：
   ```
   - 检查所有消息是否正确显示
   - 验证参与者信息是否完整
   - 确认统计数据是否准确
   - 测试不会触发新的模拟
   ```
