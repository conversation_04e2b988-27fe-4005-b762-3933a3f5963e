# 数据持久化功能指南

本指南详细介绍多智能体讨论系统的数据持久化功能，包括配置存储、历史记录管理和数据备份恢复。

## 🎯 功能概述

### 持久化的数据类型
1. **智能体配置** - 所有自定义智能体的设置和属性
2. **LLM配置** - 大语言模型的连接配置和参数
3. **讨论历史** - 完整的对话记录和共识结果
4. **应用设置** - 系统偏好和配置选项
5. **用户偏好** - 个性化的界面和行为设置

### 存储机制
- **服务器存储**: 数据保存在后端服务器数据库中
- **自动保存**: 配置变更时自动保存，无需手动操作
- **数据验证**: 加载时验证数据完整性和格式正确性
- **版本管理**: 支持数据格式的版本迁移

## 🏗️ 技术架构

### 存储服务 (`StorageService`)
```typescript
// 核心存储服务
export class StorageService {
  // 智能体管理
  saveAgents(agents: Agent[]): void
  getAgents(): Agent[]
  saveAgent(agent: Agent): void
  deleteAgent(agentId: string): void
  
  // LLM配置管理
  saveLLMConfigs(configs: LLMConfig[]): void
  getLLMConfigs(): LLMConfig[]
  saveLLMConfig(config: LLMConfig): void
  deleteLLMConfig(configId: string): void
  
  // 讨论记录管理
  saveDiscussions(discussions: Discussion[]): void
  getDiscussions(): Discussion[]
  saveDiscussion(discussion: Discussion): void
  deleteDiscussion(discussionId: string): void
  
  // 数据管理
  getAllData(): StorageData
  importAllData(data: Partial<StorageData>): void
  clearAllData(): void
}
```

### 数据结构
```typescript
interface StorageData {
  agents: Agent[];           // 智能体配置
  llmConfigs: LLMConfig[];   // LLM配置
  discussions: Discussion[]; // 讨论历史
  settings: AppSettings;     // 应用设置
  preferences: UserPreferences; // 用户偏好
}
```

## 📊 数据管理功能

### 1. 数据导出
- **功能**: 将所有配置和历史记录导出为JSON文件
- **用途**: 数据备份、系统迁移、配置分享
- **安全**: 自动隐藏API密钥等敏感信息
- **格式**: 标准JSON格式，便于阅读和编辑

```javascript
// 导出示例
{
  "agents": [...],
  "llmConfigs": [...],
  "discussions": [...],
  "settings": {...},
  "preferences": {...}
}
```

### 2. 数据导入
- **功能**: 从备份文件恢复配置和历史记录
- **验证**: 导入前验证数据格式和完整性
- **覆盖**: 导入数据会覆盖当前配置
- **错误处理**: 详细的错误信息和回滚机制

### 3. 数据清除
- **功能**: 清除所有数据并重置为默认状态
- **确认**: 需要二次确认防止误操作
- **保留**: 保留默认智能体配置
- **不可逆**: 操作不可撤销，建议先备份

### 4. 存储监控
- **使用量**: 实时显示存储空间使用情况
- **统计**: 显示各类数据的数量统计
- **限制**: 自动限制历史记录数量防止存储溢出

## 📈 历史记录管理

### 讨论历史功能
1. **完整记录**: 保存所有消息、时间戳、参与者信息
2. **搜索过滤**: 按话题、状态、时间等条件筛选
3. **详细查看**: 展开查看完整的对话过程
4. **统计分析**: 共识度、参与度等数据分析

### 历史记录结构
```typescript
interface Discussion {
  id: string;
  topic: string;
  mode: 'moderator' | 'free';
  participants: string[];
  messages: Message[];
  status: 'active' | 'consensus' | 'ended';
  consensus: string | null;
  createdAt: Date;
  consensusScore: number;
}
```

### 自动管理
- **数量限制**: 默认保存最近100个讨论
- **自动清理**: 超出限制时自动删除最旧的记录
- **优先保留**: 优先保留达成共识的重要讨论

## 🔧 使用指南

### 基本操作流程

#### 1. 数据备份
```
数据管理 → 导出数据 → 下载备份文件
```

#### 2. 数据恢复
```
数据管理 → 导入数据 → 选择备份文件 → 确认导入
```

#### 3. 查看历史
```
讨论历史 → 搜索/过滤 → 查看详情
```

#### 4. 清理数据
```
数据管理 → 清除数据 → 二次确认 → 执行清理
```

### 最佳实践

#### 1. 定期备份
- 建议每周导出一次数据备份
- 重要配置变更后立即备份
- 保存多个版本的备份文件

#### 2. 存储管理
- 定期检查存储使用情况
- 清理不需要的历史记录
- 避免存储过多大型讨论

#### 3. 数据安全
- 备份文件包含API密钥，注意保密
- 不要在公共场所分享备份文件
- 定期更新API密钥

## 🛡️ 安全考虑

### 数据保护
1. **服务器存储**: 数据安全存储在后端服务器中
2. **加密**: 敏感信息在导出时自动隐藏
3. **隔离**: 不同用户的数据完全隔离

### 隐私保护
1. **讨论内容**: 对话记录安全存储，仅用户可访问
2. **API密钥**: 导出时自动替换为占位符
3. **用户偏好**: 个人设置不会泄露

### 风险提示
1. **服务器故障**: 服务器不可用时系统无法正常工作
2. **设备更换**: 更换设备后需要重新登录或导入数据
3. **版本升级**: 重大版本升级可能需要数据迁移

## 🔄 数据迁移

### 跨设备迁移
1. 在原设备导出数据
2. 在新设备导入数据
3. 重新配置API密钥
4. 验证功能正常

### 版本升级
1. 升级前导出当前数据
2. 升级到新版本
3. 系统自动检测并迁移数据格式
4. 验证数据完整性

### 故障恢复
1. 如果数据损坏，系统会自动重置
2. 可以从备份文件恢复
3. 联系技术支持获取帮助

## 📊 监控和分析

### 存储统计
- 智能体数量
- LLM配置数量
- 历史讨论数量
- 存储空间使用量

### 使用分析
- 讨论频率统计
- 共识达成率
- 智能体活跃度
- LLM使用情况

## 🚀 高级功能

### 批量操作
- 批量导入智能体配置
- 批量删除历史记录
- 批量更新LLM设置

### 自定义配置
- 调整历史记录保存数量
- 设置自动清理策略
- 配置数据验证规则

### API集成
- 支持通过API访问存储数据
- 可以集成外部备份服务
- 支持自定义存储后端

## 🔧 故障排除

### 常见问题

1. **数据丢失**
   - 检查服务器连接状态
   - 尝试从备份恢复
   - 重新初始化系统

2. **导入失败**
   - 验证文件格式
   - 检查文件完整性
   - 确认版本兼容性

3. **存储空间不足**
   - 清理历史记录
   - 减少保存的讨论数量
   - 导出并删除旧数据

### 调试技巧
- 打开浏览器开发者工具
- 检查localStorage内容
- 查看控制台错误信息
- 使用数据验证功能

## 📝 更新日志

### v1.0.0 - 初始版本
- 基本的数据持久化功能
- 智能体和LLM配置存储
- 讨论历史记录
- 数据导入导出

### v1.1.0 - 增强版本
- 添加数据管理界面
- 历史记录查看器
- 存储使用监控
- 自动数据验证

---

如有问题或建议，请查看项目文档或提交Issue。
