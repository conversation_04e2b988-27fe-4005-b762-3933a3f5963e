# LLM配置指南

## 概述

多智能体讨论系统现在支持接入真实的大语言模型（LLM），为智能体提供更加智能和自然的对话能力。系统支持多种LLM提供商，包括OpenAI、Anthropic、Azure OpenAI等。

## 支持的LLM提供商

### 1. OpenAI
- **模型**: GPT-4, GPT-3.5-turbo等
- **API格式**: 标准OpenAI API
- **配置要求**: API密钥

### 2. Anthropic
- **模型**: Claude 3 Opus, Claude 3 Sonnet, Claude 3 Haiku
- **API格式**: Anthropic Messages API
- **配置要求**: API密钥

### 3. Azure OpenAI
- **模型**: 部署在Azure上的OpenAI模型
- **API格式**: Azure OpenAI API
- **配置要求**: API密钥 + 基础URL

### 4. 自定义提供商
- **模型**: 兼容OpenAI API格式的任何模型
- **API格式**: OpenAI兼容API
- **配置要求**: API密钥 + 基础URL

## 配置步骤

### 第一步：创建LLM配置

1. 在主页点击"LLM管理"或在导航栏选择"LLM管理"
2. 点击"新建配置"按钮
3. 选择预设配置或自定义配置：
   - **预设配置**: 选择常用的模型配置，只需填入API密钥
   - **自定义配置**: 完全自定义所有参数

### 第二步：填写配置信息

#### 基本信息
- **配置名称**: 为配置起一个易识别的名称
- **提供商**: 选择LLM提供商（OpenAI/Anthropic/Azure/自定义）
- **模型名称**: 输入具体的模型名称

#### 认证信息
- **API密钥**: 输入您的API密钥
- **基础URL**: （Azure和自定义提供商需要）

#### 高级设置
- **温度**: 控制输出的随机性（0-2，推荐0.7）
- **最大令牌数**: 限制单次响应的长度（推荐500-1000）
- **自定义系统提示词**: 为智能体添加额外的行为指导

### 第三步：测试连接

1. 填写完配置后，点击"测试连接"按钮
2. 系统会发送测试请求验证配置是否正确
3. 测试成功后，点击"保存"按钮

### 第四步：为智能体分配LLM配置

1. 进入"智能体管理"页面
2. 创建新智能体或编辑现有智能体
3. 在表单底部的"LLM配置"下拉菜单中选择已创建的配置
4. 保存智能体配置

## 配置示例

### OpenAI GPT-4配置
```
配置名称: 我的GPT-4
提供商: OpenAI
模型名称: gpt-4
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
温度: 0.7
最大令牌数: 1000
```

### Anthropic Claude配置
```
配置名称: Claude 3 Sonnet
提供商: Anthropic
模型名称: claude-3-sonnet-20240229
API密钥: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
温度: 0.7
最大令牌数: 800
```

### Azure OpenAI配置
```
配置名称: Azure GPT-4
提供商: Azure OpenAI
模型名称: gpt-4
API密钥: xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
基础URL: https://your-resource.openai.azure.com
温度: 0.7
最大令牌数: 1000
```

## 使用说明

### 智能体行为
当智能体配置了LLM后，它们的对话将由真实的AI模型生成，具有以下特点：
- 更自然和连贯的对话
- 基于智能体的专业背景和性格特征
- 能够理解上下文和讨论历史
- 提供更有深度的观点和分析

### 降级机制
如果LLM调用失败（网络问题、API限制等），系统会自动降级到原有的模拟逻辑，确保讨论能够继续进行。

### 成本控制
- 每次智能体发言都会调用LLM API，产生费用
- 建议设置合理的最大令牌数限制
- 可以只为部分智能体配置LLM，其他使用模拟逻辑

## 故障排除

### 常见问题

1. **连接测试失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证基础URL格式（Azure用户）

2. **智能体不使用LLM**
   - 确认智能体已分配LLM配置
   - 检查LLM配置是否保存成功
   - 查看浏览器控制台是否有错误信息

3. **响应质量不佳**
   - 调整温度参数
   - 增加自定义系统提示词
   - 尝试不同的模型

### 调试技巧
- 打开浏览器开发者工具查看网络请求
- 检查控制台日志了解错误详情
- 使用测试连接功能验证配置

## 安全注意事项

1. **API密钥安全**
   - 不要在公共场所输入API密钥
   - 定期轮换API密钥
   - 使用具有适当权限的API密钥

2. **数据隐私**
   - 讨论内容会发送到LLM提供商
   - 避免在讨论中包含敏感信息
   - 了解各提供商的数据处理政策

3. **成本控制**
   - 监控API使用量和费用
   - 设置合理的令牌限制
   - 考虑使用成本较低的模型进行测试

## 高级功能

### 批量配置管理
- 使用导出功能备份配置
- 通过导入功能在不同环境间迁移配置
- 支持配置模板和预设

### 性能优化
- 系统会缓存LLM响应以提高性能
- 支持并发请求处理
- 自动重试机制处理临时故障

### 监控和分析
- 查看LLM使用统计
- 分析不同配置的效果
- 监控API调用成功率

## 更新日志

### v1.0.0
- 初始LLM集成功能
- 支持OpenAI、Anthropic、Azure OpenAI
- 基本配置管理和测试功能
- 智能体LLM配置分配
- 自动降级机制

---

如有问题或建议，请查看项目文档或提交Issue。
