# 多智能体讨论系统架构设计
## 1. 系统概述
### 核心功能
- 支持2-8个智能体同时讨论
- 智能体可自定义专业领域、思维方式、可用工具
- 两种讨论模式：主持人模式 vs 自由讨论模式
- 实时展示讨论过程
- 智能判断讨论共识并自动结束
### 技术架构
- 前端: React + TypeScript + Tailwind CSS
- 状态管理: React Context + Hooks
- 模拟后端: 本地存储 + 模拟API
- UI组件: 现代化响应式设计
## 2. 数据结构设计
### 智能体配置
```typescript
interface Agent {
  id: string;
  name: string;
  avatar: string;
  expertise: string[];        // 专业领域
  thinkingStyle: string;      // 思维方式
  personality: string;        // 性格特征
  tools: string[];           // 可用工具
  isActive: boolean;
}
```
### 讨论会话
```typescript
interface Discussion {
  id: string;
  topic: string;
  mode: 'moderator' | 'free';  // 讨论模式
  participants: string[];      // 参与的智能体ID
  messages: Message[];
  status: 'active' | 'consensus' | 'ended';
  consensus: string | null;
  createdAt: Date;
}
```
### 消息结构
```typescript
interface Message {
  id: string;
  agentId: string;
  content: string;
  type: 'statement' | 'question' | 'agreement' | 'disagreement';
  timestamp: Date;
  replyTo?: string;
}
```
## 3. 讨论机制设计
### 主持人模式
1. 选择一个智能体作为主持人

2. 主持人分配发言顺序

3. 轮流发言，每轮后主持人总结

4. 主持人判断是否达成共识

### 自由讨论模式
1. 智能体根据话题相关性自主发言

2. 基于消息内容和频率调节发言权重

3. 检测观点收敛度判断共识

## 4. 共识判断算法
### 关键指标
- 观点相似度: 分析最近N条消息的观点一致性
- 争议程度: 统计不同意见的比例
- 话题收敛: 检测讨论是否围绕特定结论
- 发言频率: 智能体活跃度变化
### 判断逻辑
```
共识达成 = 观点相似度 > 80% && 争议程度 < 20% && 连续3轮无新观点
```
## 5. 智能体行为模拟
### 发言策略
- 专业导向: 根据expertise判断发言优先级
- 性格影响: 不同personality影响表达方式
- 工具使用: 根据配置的tools进行"数据查询"等行为
### 交互行为
- 同意/反对: 基于观点相似度
- 提问: 针对不明确的观点
- 总结: 整理讨论要点
- 建议: 提出解决方案