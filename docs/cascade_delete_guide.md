# 级联删除功能指南

## 概述

为了解决删除操作中的外键约束问题，我们实现了级联删除功能，确保删除操作能够成功执行，同时保持数据一致性。

## 数据库约束更改

### 外键约束配置

1. **Message 表**
   - `agent_id` → `agents.id` (CASCADE DELETE)
   - `discussion_id` → `discussions.id` (CASCADE DELETE)

2. **Agent 表**
   - `llm_config_id` → `llm_configs.id` (RESTRICT DELETE)

3. **Discussion 表**
   - `moderator_id` → `agents.id` (SET NULL)

### 级联删除行为

- **删除智能体**: 自动删除该智能体的所有消息
- **删除讨论**: 自动删除该讨论中的所有消息
- **删除LLM配置**: 需要先删除使用该配置的智能体
- **删除主持人**: 将相关讨论的主持人字段设为NULL

## API 端点

### 标准删除操作

#### 删除智能体
```http
DELETE /api/agents/{agent_id}
```
- 自动级联删除相关消息
- 返回删除的消息数量

#### 删除讨论
```http
DELETE /api/discussions/{discussion_id}
```
- 自动级联删除相关消息
- 返回删除的消息数量

#### 删除LLM配置
```http
DELETE /api/llm-configs/{config_id}
```
- 检查是否有智能体在使用
- 如果有使用者则拒绝删除

### 强制删除操作

#### 强制删除LLM配置
```http
DELETE /api/llm-configs/{config_id}/force-delete
```
- 先删除使用该配置的所有智能体
- 再删除LLM配置本身
- 返回删除的智能体和消息数量

### 批量删除操作

#### 批量删除智能体
```http
DELETE /api/agents/batch-delete
Content-Type: application/json

{
  "agentIds": ["agent1", "agent2", "agent3"]
}
```

#### 批量删除讨论
```http
DELETE /api/discussions/batch-delete
Content-Type: application/json

{
  "discussionIds": ["discussion1", "discussion2", "discussion3"]
}
```

## 迁移脚本

### 应用级联约束
```bash
# 检查当前约束
python scripts/migrate_cascade_constraints.py --check

# 执行约束迁移
python scripts/migrate_cascade_constraints.py --migrate

# 测试级联删除
python scripts/migrate_cascade_constraints.py --test
```

### 修复现有数据
```bash
# 检查数据完整性
python scripts/fix_null_agent_ids.py --check

# 修复无效数据
python scripts/fix_null_agent_ids.py
```

## 使用示例

### 前端删除操作

```typescript
// 删除智能体（自动级联删除消息）
const deleteAgent = async (agentId: string) => {
  try {
    const response = await apiService.deleteAgent(agentId);
    console.log(`删除成功，同时删除了 ${response.deletedMessages} 条消息`);
  } catch (error) {
    console.error('删除失败:', error);
  }
};

// 批量删除智能体
const batchDeleteAgents = async (agentIds: string[]) => {
  try {
    const response = await fetch('/api/agents/batch-delete', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ agentIds })
    });
    const result = await response.json();
    console.log(`批量删除成功: ${result.deletedAgents} 个智能体, ${result.deletedMessages} 条消息`);
  } catch (error) {
    console.error('批量删除失败:', error);
  }
};

// 强制删除LLM配置
const forceDeleteLLMConfig = async (configId: string) => {
  try {
    const response = await fetch(`/api/llm-configs/${configId}/force-delete`, {
      method: 'DELETE'
    });
    const result = await response.json();
    console.log(`强制删除成功，同时删除了 ${result.deletedAgents} 个智能体`);
  } catch (error) {
    console.error('强制删除失败:', error);
  }
};
```

## 安全考虑

### 删除确认
建议在前端实现删除确认机制：

```typescript
const confirmDelete = (itemType: string, itemName: string, cascadeInfo?: string) => {
  const message = cascadeInfo 
    ? `确定要删除${itemType}"${itemName}"吗？\n${cascadeInfo}`
    : `确定要删除${itemType}"${itemName}"吗？`;
  
  return window.confirm(message);
};

// 使用示例
if (confirmDelete('智能体', agent.name, '这将同时删除该智能体的所有消息')) {
  await deleteAgent(agent.id);
}
```

### 权限控制
- 强制删除操作应该有额外的权限检查
- 批量删除操作应该限制数量
- 记录删除操作的日志

## 故障排除

### 常见问题

1. **删除失败 - 外键约束错误**
   ```
   解决方案: 运行约束迁移脚本
   python scripts/migrate_cascade_constraints.py --migrate
   ```

2. **数据不一致**
   ```
   解决方案: 运行数据修复脚本
   python scripts/fix_null_agent_ids.py
   ```

3. **级联删除不生效**
   ```
   检查外键约束是否启用:
   python scripts/migrate_cascade_constraints.py --check
   ```

### 数据恢复

如果误删除了重要数据：

1. **从备份恢复**（推荐）
2. **使用数据导入功能恢复部分数据**
3. **检查应用日志获取删除详情**

## 最佳实践

1. **删除前备份**: 重要数据删除前先备份
2. **分步删除**: 大量数据分批删除，避免长时间锁定
3. **确认机制**: 实现多级确认，特别是批量和强制删除
4. **日志记录**: 记录所有删除操作的详细信息
5. **权限控制**: 限制危险删除操作的权限

## 相关文件

- `backend/models.py` - 数据模型和约束定义
- `backend/routes.py` - 删除API端点
- `scripts/migrate_cascade_constraints.py` - 约束迁移脚本
- `scripts/fix_null_agent_ids.py` - 数据修复脚本
- `docs/cascade_delete_guide.md` - 本文档
