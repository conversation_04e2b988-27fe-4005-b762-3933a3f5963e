# 多智能体讨论系统演示指南

本指南将带您快速体验多智能体讨论系统的核心功能，包括LLM集成能力。

## 🎯 演示目标

通过本演示，您将学会：
1. 创建和配置智能体
2. 设置LLM配置（可选）
3. 启动智能体讨论
4. 观察AI对话和共识形成过程

## 📋 准备工作

### 1. 启动应用
```bash
npm install
npm run dev
```
访问 http://localhost:3000

### 2. 准备API密钥（可选）
如果要体验真实LLM功能，请准备以下任一API密钥：
- OpenAI API密钥 (推荐)
- Anthropic API密钥
- Azure OpenAI配置

## 🚀 快速演示流程

### 第一步：创建智能体团队

1. **进入智能体管理页面**
   - 点击主页的"智能体管理"卡片
   - 或使用顶部导航栏

2. **创建第一个智能体 - 技术专家**
   ```
   名称: Alex
   专业领域: 技术、数据分析
   思维方式: 逻辑型
   性格特征: 分析型
   工具: 数据查询、技术调研
   ```

3. **创建第二个智能体 - 商业分析师**
   ```
   名称: Luna
   专业领域: 商业、营销
   思维方式: 分析型
   性格特征: 协作型
   工具: 市场分析、竞品分析
   ```

4. **创建第三个智能体 - 创意设计师**
   ```
   名称: Robin
   专业领域: 设计、心理学
   思维方式: 创意型
   性格特征: 外交型
   工具: 用户调研、创意生成
   ```

### 第二步：配置LLM（可选但推荐）

1. **进入LLM管理页面**
   - 点击主页的"LLM管理"卡片
   - 或使用顶部导航栏的"LLM管理"

2. **创建OpenAI配置**（如果有API密钥）
   ```
   配置名称: GPT-4配置
   提供商: OpenAI
   模型名称: gpt-4
   API密钥: [您的API密钥]
   温度: 0.7
   最大令牌数: 800
   ```

3. **测试连接**
   - 点击"测试连接"按钮
   - 确认配置正确后保存

4. **为智能体分配LLM**
   - 回到智能体管理页面
   - 编辑每个智能体
   - 在"LLM配置"下拉菜单中选择刚创建的配置

### 第三步：创建讨论

1. **进入讨论设置页面**
   - 点击主页的"创建讨论"卡片
   - 或使用顶部导航栏

2. **配置讨论参数**
   ```
   讨论话题: "如何设计一个成功的移动应用"
   讨论模式: 自由讨论模式
   参与智能体: 选择所有三个智能体
   ```

3. **启动讨论**
   - 点击"开始讨论"按钮
   - 系统自动跳转到讨论室

### 第四步：观察讨论过程

1. **实时对话监控**
   - 观察智能体轮流发言
   - 注意不同智能体的专业视角差异
   - 查看消息类型（陈述、提问、同意、反对）

2. **共识度监控**
   - 右侧面板显示实时共识度
   - 观察共识度随讨论进展的变化
   - 注意参与者活跃度统计

3. **讨论特点观察**
   - **Alex（技术专家）**: 关注技术实现、数据分析
   - **Luna（商业分析师）**: 关注市场需求、商业模式
   - **Robin（创意设计师）**: 关注用户体验、界面设计

## 🎭 演示场景建议

### 场景1：产品开发讨论
**话题**: "开发一个AI驱动的健康管理应用"
**预期效果**: 技术、商业、设计三个维度的全面分析

### 场景2：市场策略讨论
**话题**: "如何在竞争激烈的市场中推广新产品"
**预期效果**: 营销策略、用户心理、数据分析的结合

### 场景3：技术架构讨论
**话题**: "设计一个可扩展的微服务架构"
**预期效果**: 技术深度讨论，展示专业知识应用

## 🔍 观察要点

### LLM vs 模拟逻辑对比
1. **使用LLM的智能体**:
   - 回复更自然和连贯
   - 能够理解上下文
   - 提供更深入的专业见解
   - 回复长度和质量更高

2. **使用模拟逻辑的智能体**:
   - 回复基于预设模板
   - 相对简单和固定
   - 响应速度更快
   - 无API调用成本

### 讨论动态观察
1. **发言模式**:
   - 自由模式：基于相关性自主发言
   - 主持人模式：有序轮流发言

2. **共识形成**:
   - 初期：观点分散，争议较多
   - 中期：开始出现一致观点
   - 后期：达成共识，讨论收敛

3. **智能体特征**:
   - 专业领域影响发言内容
   - 性格特征影响表达方式
   - 思维方式影响分析角度

## 🛠️ 高级功能演示

### 1. 配置导入导出
- 导出LLM配置进行备份
- 在不同环境间迁移配置

### 2. 讨论模式切换
- 体验主持人模式的有序讨论
- 对比自由模式的动态交互

### 3. 智能体个性化
- 调整智能体的专业领域组合
- 测试不同性格特征的影响

### 4. 实时监控
- 观察参与者活跃度变化
- 分析消息类型分布
- 监控共识度演变

## 🎯 演示技巧

### 1. 话题选择
- 选择有争议性的话题更容易观察到不同观点
- 技术性话题能更好展示专业知识差异
- 开放性问题有利于创意发挥

### 2. 智能体配置
- 确保智能体有明显的专业差异
- 混合不同的性格特征增加讨论多样性
- 适当的工具配置增强专业性

### 3. 观察重点
- 关注智能体如何运用专业知识
- 观察不同思维方式的体现
- 注意共识形成的过程和时机

## 🚨 注意事项

### 1. API使用
- LLM调用会产生费用，请合理使用
- 建议先用少量智能体测试
- 可以混合使用LLM和模拟逻辑

### 2. 性能考虑
- 过多智能体可能影响响应速度
- LLM调用有网络延迟
- 建议3-5个智能体为最佳体验

### 3. 内容质量
- LLM生成的内容质量取决于配置
- 适当调整温度参数优化输出
- 自定义系统提示词可以改善效果

## 📊 演示结果分析

完成演示后，您可以：
1. 查看完整的讨论记录
2. 分析共识形成过程
3. 比较不同智能体的贡献
4. 评估LLM集成的效果

## 🎉 演示总结

通过本演示，您应该能够：
- ✅ 理解多智能体讨论的基本概念
- ✅ 掌握LLM配置和集成方法
- ✅ 观察到AI智能体的协作过程
- ✅ 体验智能共识判断功能
- ✅ 了解系统的扩展可能性

## 🔗 下一步

1. 尝试更复杂的讨论场景
2. 探索不同LLM提供商的效果
3. 自定义智能体的专业配置
4. 开发新的讨论模式和功能

---

**提示**: 如果在演示过程中遇到问题，请查看浏览器控制台的错误信息，或参考故障排除指南。
