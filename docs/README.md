# 多智能体讨论系统

一个基于React和TypeScript构建的智能多智能体讨论系统，支持真实LLM接入。现已支持服务器端数据持久化，解决了浏览器清理数据导致的数据丢失问题。

## ✨ 主要功能

### 🤖 智能体管理
- 创建和配置具有不同专业背景的AI智能体
- 支持多种思维方式：逻辑型、创意型、分析型、直觉型、系统型
- 丰富的性格特征：果断型、协作型、外交型、直接型、深思型
- 可配置专业工具和知识领域

### 🧠 LLM集成 (新功能)
- **多提供商支持**: OpenAI、Anthropic、Azure OpenAI、自定义API
- **模型选择**: GPT-4, GPT-3.5-turbo, Claude 3系列等
- **个性化配置**: 温度、令牌数、系统提示词等参数
- **连接测试**: 内置配置验证功能
- **降级机制**: LLM失败时自动切换到模拟逻辑
- **配置管理**: 导入/导出、批量管理功能

### 💬 讨论系统
- **两种讨论模式**:
  - 主持人模式：有序轮流发言
  - 自由讨论模式：基于相关性自主发言
- **智能共识判断**: 实时分析讨论进度和共识度
- **多种消息类型**: 陈述、提问、同意、反对
- **实时监控**: 动态显示讨论状态和参与度

### 📊 分析功能
- 实时共识度计算
- 参与者活跃度统计
- 讨论历史记录
- 消息类型分析

### 💾 数据持久化 (新功能)
- **完整数据存储**: 智能体配置、LLM设置、讨论历史全面持久化
- **服务器存储**: 数据安全保存在后端服务器数据库中
- **自动保存**: 配置变更时自动保存，无需手动操作
- **数据备份**: 一键导出所有数据为JSON文件
- **数据恢复**: 从备份文件快速恢复配置和历史
- **历史查看**: 完整的讨论历史记录查看和搜索
- **存储监控**: 实时显示存储使用情况和统计信息

## 🚀 快速开始

### 环境要求
- Node.js 16+
- Python 3.8+
- npm 或 yarn
- pip (Python包管理器)

### 快速启动

#### 方式一：使用启动脚本（推荐）

**Linux/macOS:**
```bash
./start.sh
```

**Windows:**
```cmd
start.bat
```

启动脚本会自动：
- 检查并安装依赖
- 初始化数据库
- 启动后端服务（端口5000）
- 启动前端服务（端口5173）

#### 方式二：手动启动

**1. 安装前端依赖并启动**
```bash
# 克隆项目
git clone <repository-url>
cd multi_chat

# 安装前端依赖
npm install

# 启动前端开发服务器
npm run dev
```

**2. 安装后端依赖并启动**
```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python init_db.py

# 启动后端服务器
python app.py
```

**3. 访问应用**
- 前端地址: http://localhost:3000
- 后端地址: http://localhost:5000

### 基本使用流程

1. **确保服务器运行**
   - 确保后端服务正常运行
   - 系统使用服务器存储模式

2. **配置LLM**
   - 进入"LLM管理"页面
   - 创建LLM配置（需要API密钥）
   - 测试配置确保连接正常

3. **创建智能体**
   - 进入"智能体管理"页面
   - 点击"添加智能体"
   - 配置名称、专业领域、思维方式等
   - 分配LLM配置

4. **创建讨论**
   - 进入"创建讨论"页面
   - 设置讨论话题和模式
   - 选择参与的智能体

5. **观看讨论**
   - 系统自动开始智能体对话
   - 实时监控共识度变化
   - 查看讨论结果和总结

6. **管理数据**
   - 查看讨论历史记录
   - 导出数据进行备份
   - 导入配置进行恢复

## 🔧 LLM配置指南

详细的LLM配置说明请参考 [LLM_SETUP_GUIDE.md](./LLM_SETUP_GUIDE.md)

### 支持的LLM提供商

| 提供商 | 支持模型 | 配置要求 |
|--------|----------|----------|
| OpenAI | GPT-4, GPT-3.5-turbo | API密钥 |
| Anthropic | Claude 3 Opus/Sonnet/Haiku | API密钥 |
| Azure OpenAI | Azure部署的OpenAI模型 | API密钥 + 基础URL |
| 自定义 | OpenAI兼容API | API密钥 + 基础URL |

### 配置示例

```javascript
// OpenAI配置
{
  name: "GPT-4配置",
  provider: "openai",
  model: "gpt-4",
  apiKey: "sk-...",
  temperature: 0.7,
  maxTokens: 1000
}

// Anthropic配置
{
  name: "Claude配置", 
  provider: "anthropic",
  model: "claude-3-sonnet-20240229",
  apiKey: "sk-ant-...",
  temperature: 0.7,
  maxTokens: 800
}
```

## 🏗️ 技术架构

### 前端技术栈
- **React 18**: 用户界面框架
- **TypeScript**: 类型安全的JavaScript
- **Tailwind CSS**: 实用优先的CSS框架
- **Lucide React**: 现代图标库
- **Vite**: 快速构建工具

### 核心模块

#### 1. 智能体系统 (`src/components/AgentManager.tsx`)
- 智能体创建和编辑
- 属性配置和管理
- LLM配置分配

#### 2. LLM服务 (`src/services/llmService.ts`)
- 统一的LLM API调用接口
- 多提供商适配器
- 错误处理和重试机制

#### 3. 讨论引擎 (`src/utils/aiLogic.ts`)
- 消息生成逻辑
- 共识度计算算法
- 发言顺序控制

#### 4. 配置管理 (`src/utils/llmConfig.ts`)
- LLM配置的CRUD操作
- 配置验证和测试
- 导入/导出功能

#### 5. 数据持久化 (`src/services/storageService.ts`)
- 统一的数据存储接口
- 自动保存和加载机制
- 数据验证和迁移
- 备份和恢复功能

### 数据流

```
用户输入 → 智能体配置 → LLM调用 → 消息生成 → 讨论更新 → UI渲染
```

## 📁 项目结构

```
src/
├── components/          # React组件
│   ├── AgentManager.tsx    # 智能体管理
│   ├── LLMManager.tsx      # LLM配置管理
│   ├── LLMConfigModal.tsx  # LLM配置弹窗
│   ├── DataManager.tsx     # 数据管理
│   ├── DiscussionHistory.tsx # 讨论历史
│   ├── LoadingScreen.tsx   # 加载界面
│   ├── DiscussionSetup.tsx # 讨论设置
│   └── DiscussionRoom.tsx  # 讨论室
├── services/            # 服务层
│   ├── llmService.ts       # LLM服务
│   └── storageService.ts   # 存储服务
├── utils/              # 工具函数
│   ├── aiLogic.ts         # AI逻辑
│   └── llmConfig.ts       # LLM配置工具
├── types/              # TypeScript类型定义
│   └── index.ts
├── context/            # React Context
│   └── AppContext.tsx
└── data/               # 静态数据
    └── defaultLLMConfigs.ts
```

## 🔒 安全考虑

### API密钥安全
- 所有API密钥存储在浏览器本地存储中
- 不会发送到任何第三方服务器
- 建议定期轮换API密钥

### 数据隐私
- 讨论内容会发送到配置的LLM提供商
- 请避免在讨论中包含敏感信息
- 了解各LLM提供商的数据处理政策

### 成本控制
- 每次智能体发言都会产生API调用费用
- 建议设置合理的令牌数限制
- 可以混合使用LLM和模拟逻辑

### 数据安全
- 所有数据存储在服务器数据库中，确保数据持久化
- 支持数据导出备份和导入恢复
- 建议定期备份重要配置和讨论记录

## 🛠️ 开发指南

### 添加新的LLM提供商

1. 在 `llmService.ts` 中添加新的提供商处理逻辑
2. 更新 `LLMConfig` 类型定义
3. 在 `LLMConfigModal.tsx` 中添加UI支持
4. 更新配置验证逻辑

### 自定义智能体行为

1. 修改 `aiLogic.ts` 中的消息生成逻辑
2. 添加新的思维方式或性格类型
3. 扩展专业领域和工具选项

### 扩展讨论功能

1. 在 `DiscussionRoom.tsx` 中添加新的UI组件
2. 扩展消息类型和处理逻辑
3. 增强共识判断算法

## 🐛 故障排除

### 常见问题

1. **LLM连接失败**
   - 检查API密钥格式
   - 验证网络连接
   - 确认提供商服务状态

2. **智能体不响应**
   - 检查智能体是否配置了LLM
   - 查看浏览器控制台错误
   - 验证讨论状态

3. **性能问题**
   - 减少同时参与的智能体数量
   - 降低消息生成频率
   - 使用更快的LLM模型

## 📈 未来规划

- [ ] 支持更多LLM提供商
- [ ] 添加讨论模板和预设
- [ ] 增加实时协作功能
- [ ] 支持多语言界面
- [ ] 添加讨论分析报告
- [ ] 实现智能体学习功能
- [ ] 云端数据同步
- [ ] 高级数据分析和可视化
- [ ] 讨论结果自动总结
- [ ] 智能推荐系统

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢所有贡献者和开源社区的支持！

---

**注意**: 使用LLM功能需要相应的API密钥，请确保遵守各提供商的使用条款和定价政策。
